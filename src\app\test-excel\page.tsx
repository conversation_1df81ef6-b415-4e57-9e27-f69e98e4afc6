"use client";

import { useState } from "react";
import { useToast } from "@/components/ToastProvider";

export default function TestExcelPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const { showToast } = useToast();

  // 测试API调用格式
  const testApiCall = async () => {
    const testData = {
      urls: [
        {
          url: "https://pan.quark.cn/s/3cb03fd7fb87",
          title: "测试文件名123456"
        },
        {
          url: "https://pan.baidu.com/s/1example123",
          title: "百度网盘测试文件"
        }
      ],
      is_mine: false,
      is_parsed: true,
      admin_submit: false
    };

    setIsSubmitting(true);
    try {
      console.log("发送请求数据:", JSON.stringify(testData, null, 2));
      
      const response = await fetch("/api/submit_resources", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(testData),
      });

      const data = await response.json();
      console.log("响应数据:", data);

      if (!response.ok) {
        throw new Error(data.message || "提交失败");
      }

      setResult(data);
      showToast("API测试成功！", "success");
    } catch (error: any) {
      console.error("API测试失败:", error);
      showToast(error.message || "API测试失败", "error");
      setResult({ error: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Excel导入API测试</h1>
      
      <div className="space-y-6">
        <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
          <h2 className="text-lg font-semibold mb-4">API请求格式测试</h2>
          <p className="text-[var(--secondary-text)] mb-4">
            测试新的API请求格式，包含url、title和必要的参数
          </p>
          
          <div className="mb-4">
            <h3 className="font-medium mb-2">请求数据格式：</h3>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded text-sm overflow-x-auto">
{`{
  "urls": [
    {
      "url": "https://pan.quark.cn/s/3cb03fd7fb87",
      "title": "测试文件名123456"
    }
  ],
  "is_mine": false,
  "is_parsed": true,
  "admin_submit": false
}`}
            </pre>
          </div>
          
          <button
            onClick={testApiCall}
            disabled={isSubmitting}
            className="px-4 py-2 bg-[var(--button-background)] hover:bg-[var(--button-hover)] text-white rounded-lg transition-colors duration-200 disabled:opacity-50"
          >
            {isSubmitting ? "测试中..." : "测试API调用"}
          </button>
        </div>

        {result && (
          <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
            <h2 className="text-lg font-semibold mb-4">API响应结果</h2>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded text-sm overflow-x-auto whitespace-pre-wrap">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="bg-[var(--card-background)] p-6 rounded-xl shadow-sm border border-[var(--border-color)]">
          <h2 className="text-lg font-semibold mb-4">Excel导入功能说明</h2>
          <ul className="space-y-2 text-[var(--secondary-text)]">
            <li>✅ 支持URL和标题两列格式</li>
            <li>✅ 直接调用submit_resources API</li>
            <li>✅ 包含is_mine、is_parsed、admin_submit参数</li>
            <li>✅ 移除了描述字段支持</li>
            <li>✅ 实时数据验证和预览</li>
            <li>✅ 错误处理和用户提示</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
