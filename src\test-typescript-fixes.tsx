/**
 * TypeScript修复验证测试
 */

import React from 'react';
import { useAuth } from './hooks/useAuth';

// 测试useAuth hook的类型
function TestAuthHook() {
  const { 
    user, 
    isLoading, 
    isAuthenticated, 
    logout, 
    refreshUser, 
    checkAuth, 
    setUser, // 这个方法现在应该存在
    hasPermission, 
    isAdmin, 
    isModerator, 
    requireAuth, 
    requirePermission, 
    requireAdmin 
  } = useAuth();

  // 测试setUser方法
  const handleSetUser = () => {
    setUser({
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      nickname: 'Test User',
      avatar: null,
      status: 'active',
      email_verified: true,
      role: { id: '1', name: 'user' },
      title: 'User',
      points: 0,
      created_at: new Date().toISOString(),
    });
  };

  return (
    <div>
      <h2>useAuth Hook 测试</h2>
      <p>用户: {user?.username || '未登录'}</p>
      <p>加载中: {isLoading ? '是' : '否'}</p>
      <p>已认证: {isAuthenticated ? '是' : '否'}</p>
      <p>是管理员: {isAdmin() ? '是' : '否'}</p>
      
      <button onClick={handleSetUser}>
        测试 setUser 方法
      </button>
      
      <button onClick={logout}>
        登出
      </button>
      
      <button onClick={refreshUser}>
        刷新用户信息
      </button>
      
      <button onClick={checkAuth}>
        检查认证状态
      </button>
    </div>
  );
}

// 测试searchParams可能为null的处理
function TestSearchParams() {
  // 模拟useSearchParams可能返回null的情况
  const searchParams = null; // 在实际使用中这来自useSearchParams()
  
  // 这种写法现在应该不会报TypeScript错误
  const redirectTo = searchParams?.get("redirect") || "/";
  const message = searchParams?.get("message");
  
  return (
    <div>
      <h2>SearchParams 测试</h2>
      <p>重定向到: {redirectTo}</p>
      <p>消息: {message || '无消息'}</p>
    </div>
  );
}

// 测试错误处理（不使用catch参数）
function TestErrorHandling() {
  const handleAsyncOperation = async () => {
    try {
      // 模拟可能失败的异步操作
      await fetch('/api/test');
    } catch {
      // 不使用catch参数，避免ESLint警告
      console.error("操作失败");
    }
  };

  return (
    <div>
      <h2>错误处理测试</h2>
      <button onClick={handleAsyncOperation}>
        测试异步操作
      </button>
    </div>
  );
}

// 主测试组件
export default function TypeScriptFixesTest() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>TypeScript 修复验证</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <TestAuthHook />
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <TestSearchParams />
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <TestErrorHandling />
      </div>
      
      <div style={{ 
        padding: '10px', 
        backgroundColor: '#f0f0f0', 
        borderRadius: '5px',
        marginTop: '20px'
      }}>
        <h3>修复总结</h3>
        <ul>
          <li>✅ 添加了 setUser 方法到 useAuth hook</li>
          <li>✅ 修复了 searchParams 可能为 null 的问题</li>
          <li>✅ 移除了未使用的 catch 参数</li>
          <li>✅ 所有 TypeScript 错误已解决</li>
        </ul>
      </div>
    </div>
  );
}
