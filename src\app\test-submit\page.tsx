"use client";

import { useState } from "react";

export default function TestSubmitPage() {
  const [message, setMessage] = useState("Excel导入功能测试页面");

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">测试页面</h1>
      <p className="mb-4">{message}</p>
      <button
        onClick={() => setMessage("按钮点击成功！")}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        测试按钮
      </button>
      
      <div className="mt-8 p-4 border border-gray-300 rounded">
        <h2 className="text-lg font-semibold mb-2">Excel导入功能状态</h2>
        <ul className="space-y-2">
          <li>✅ 基础React组件正常</li>
          <li>✅ TypeScript编译正常</li>
          <li>✅ 样式系统正常</li>
          <li>⏳ Excel导入组件待测试</li>
        </ul>
      </div>
    </div>
  );
}
