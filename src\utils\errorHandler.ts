/**
 * 统一的错误处理工具
 */

export interface ApiError {
  code?: string;
  message: string;
  detail?: string;
  field?: string;
}

export interface ErrorResponse {
  success: false;
  message: string;
  error?: string;
  errors?: ApiError[];
}

/**
 * 解析后端API错误响应
 */
export function parseApiError(response: any): ErrorResponse {
  // 处理FastAPI的验证错误格式
  if (response.detail && Array.isArray(response.detail)) {
    const errors: ApiError[] = response.detail.map((error: any) => ({
      code: error.type,
      message: error.msg,
      field: error.loc?.join("."),
    }));

    return {
      success: false,
      message: "请求参数验证失败",
      errors,
    };
  }

  // 处理单个错误信息
  if (response.detail) {
    return {
      success: false,
      message: response.detail,
      error: response.detail,
    };
  }

  // 处理自定义错误格式
  if (response.message) {
    return {
      success: false,
      message: response.message,
      error: response.error,
    };
  }

  // 默认错误处理
  return {
    success: false,
    message: "请求失败",
    error: "未知错误",
  };
}

/**
 * 网络错误处理
 */
export function handleNetworkError(error: Error): ErrorResponse {
  console.error("网络请求失败:", error);

  if (error.name === "TypeError" && error.message.includes("fetch")) {
    return {
      success: false,
      message: "网络连接失败，请检查网络连接",
      error: "网络错误",
    };
  }

  if (error.name === "AbortError") {
    return {
      success: false,
      message: "请求超时，请稍后重试",
      error: "请求超时",
    };
  }

  return {
    success: false,
    message: "网络错误，请稍后重试",
    error: error.message,
  };
}

/**
 * 认证错误处理
 */
export function handleAuthError(status: number, response: any): ErrorResponse {
  switch (status) {
    case 401:
      return {
        success: false,
        message: "登录已过期，请重新登录",
        error: "未授权",
      };
    case 403:
      return {
        success: false,
        message: "权限不足，无法访问此资源",
        error: "权限不足",
      };
    case 422:
      return parseApiError(response);
    default:
      return parseApiError(response);
  }
}

/**
 * 获取用户友好的错误消息
 */
export function getUserFriendlyMessage(error: ApiError): string {
  const errorMessages: Record<string, string> = {
    // 验证错误
    "value_error.missing": "必填字段不能为空",
    "value_error.email": "请输入有效的邮箱地址",
    "value_error.str.min_length": "输入内容过短",
    "value_error.str.max_length": "输入内容过长",
    "type_error.none.not_allowed": "此字段不能为空",

    // 认证错误
    invalid_credentials: "用户名或密码错误",
    user_not_found: "用户不存在",
    email_already_exists: "邮箱已被注册",
    username_already_exists: "用户名已被使用",
    invalid_token: "无效的令牌",
    token_expired: "令牌已过期",
    email_not_verified: "邮箱未验证",
    account_disabled: "账户已被禁用",
    account_locked: "账户已被锁定",

    // 密码错误
    password_too_weak: "密码强度不够",
    password_mismatch: "两次输入的密码不一致",
    old_password_incorrect: "原密码错误",

    // 个人信息管理错误
    profile_update_failed: "个人信息更新失败",
    avatar_upload_failed: "头像上传失败",
    avatar_too_large: "头像文件过大，请选择小于2MB的图片",
    avatar_invalid_format: "头像格式不支持，请选择JPG、PNG或GIF格式",
    email_change_failed: "邮箱修改失败",
    email_verification_failed: "邮箱验证失败",
    email_verification_expired: "邮箱验证链接已过期",
    email_already_in_use: "该邮箱已被其他用户使用",
    nickname_too_short: "昵称长度不能少于2个字符",
    nickname_too_long: "昵称长度不能超过20个字符",
    nickname_invalid_chars: "昵称包含非法字符",
    nickname_already_taken: "该昵称已被使用",
    nickname_change_too_frequent: "昵称修改过于频繁，请稍后再试",
    points_insufficient: "积分不足",
    operation_not_allowed: "当前状态下不允许此操作",
  };

  return errorMessages[error.code || ""] || error.message;
}

/**
 * 个人信息管理错误处理
 */
export function handleProfileError(
  status: number,
  response: any
): ErrorResponse {
  switch (status) {
    case 400:
      // 处理文件上传相关错误
      if (response.detail && response.detail.includes("file")) {
        if (response.detail.includes("size")) {
          return {
            success: false,
            message: "头像文件过大，请选择小于2MB的图片",
            error: "avatar_too_large",
          };
        }
        if (
          response.detail.includes("format") ||
          response.detail.includes("type")
        ) {
          return {
            success: false,
            message: "头像格式不支持，请选择JPG、PNG或GIF格式",
            error: "avatar_invalid_format",
          };
        }
      }

      // 处理昵称相关错误
      if (response.detail && response.detail.includes("nickname")) {
        if (
          response.detail.includes("length") ||
          response.detail.includes("short")
        ) {
          return {
            success: false,
            message: "昵称长度不能少于2个字符",
            error: "nickname_too_short",
          };
        }
        if (response.detail.includes("long")) {
          return {
            success: false,
            message: "昵称长度不能超过20个字符",
            error: "nickname_too_long",
          };
        }
        if (
          response.detail.includes("taken") ||
          response.detail.includes("exists")
        ) {
          return {
            success: false,
            message: "该昵称已被使用",
            error: "nickname_already_taken",
          };
        }
      }

      // 处理邮箱相关错误
      if (response.detail && response.detail.includes("email")) {
        if (
          response.detail.includes("use") ||
          response.detail.includes("exists")
        ) {
          return {
            success: false,
            message: "该邮箱已被其他用户使用",
            error: "email_already_in_use",
          };
        }
      }

      return parseApiError(response);

    case 401:
      return {
        success: false,
        message: "登录已过期，请重新登录",
        error: "未授权",
      };

    case 403:
      return {
        success: false,
        message: "权限不足，无法执行此操作",
        error: "权限不足",
      };

    case 409:
      return {
        success: false,
        message: "操作冲突，请稍后重试",
        error: "操作冲突",
      };

    case 413:
      return {
        success: false,
        message: "文件过大，请选择较小的文件",
        error: "文件过大",
      };

    case 422:
      return parseApiError(response);

    case 429:
      return {
        success: false,
        message: "操作过于频繁，请稍后再试",
        error: "请求过于频繁",
      };

    default:
      return parseApiError(response);
  }
}

/**
 * 验证头像文件
 */
export function validateAvatarFile(file: File): {
  valid: boolean;
  error?: string;
} {
  // 检查文件大小 (2MB)
  const maxSize = 2 * 1024 * 1024;
  if (file.size > maxSize) {
    return {
      valid: false,
      error: "头像文件过大，请选择小于2MB的图片",
    };
  }

  // 检查文件类型
  const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: "头像格式不支持，请选择JPG、PNG或GIF格式",
    };
  }

  return { valid: true };
}

/**
 * 验证昵称
 */
export function validateNickname(nickname: string): {
  valid: boolean;
  error?: string;
} {
  if (!nickname || nickname.trim().length < 2) {
    return {
      valid: false,
      error: "昵称长度不能少于2个字符",
    };
  }

  if (nickname.length > 20) {
    return {
      valid: false,
      error: "昵称长度不能超过20个字符",
    };
  }

  // 检查非法字符（只允许中文、英文、数字、下划线）
  const validPattern = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/;
  if (!validPattern.test(nickname)) {
    return {
      valid: false,
      error: "昵称只能包含中文、英文、数字和下划线",
    };
  }

  return { valid: true };
}

/**
 * 格式化错误消息用于显示
 */
export function formatErrorMessage(errorResponse: ErrorResponse): string {
  if (errorResponse.errors && errorResponse.errors.length > 0) {
    // 如果有多个错误，显示第一个用户友好的错误
    const firstError = errorResponse.errors[0];
    return getUserFriendlyMessage(firstError);
  }

  return errorResponse.message;
}
