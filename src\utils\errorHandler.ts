/**
 * 统一的错误处理工具
 */

export interface ApiError {
  code?: string;
  message: string;
  detail?: string;
  field?: string;
}

export interface ErrorResponse {
  success: false;
  message: string;
  error?: string;
  errors?: ApiError[];
}

/**
 * 解析后端API错误响应
 */
export function parseApiError(response: any): ErrorResponse {
  // 处理FastAPI的验证错误格式
  if (response.detail && Array.isArray(response.detail)) {
    const errors: ApiError[] = response.detail.map((error: any) => ({
      code: error.type,
      message: error.msg,
      field: error.loc?.join('.'),
    }));

    return {
      success: false,
      message: "请求参数验证失败",
      errors,
    };
  }

  // 处理单个错误信息
  if (response.detail) {
    return {
      success: false,
      message: response.detail,
      error: response.detail,
    };
  }

  // 处理自定义错误格式
  if (response.message) {
    return {
      success: false,
      message: response.message,
      error: response.error,
    };
  }

  // 默认错误处理
  return {
    success: false,
    message: "请求失败",
    error: "未知错误",
  };
}

/**
 * 网络错误处理
 */
export function handleNetworkError(error: Error): ErrorResponse {
  console.error("网络请求失败:", error);

  if (error.name === "TypeError" && error.message.includes("fetch")) {
    return {
      success: false,
      message: "网络连接失败，请检查网络连接",
      error: "网络错误",
    };
  }

  if (error.name === "AbortError") {
    return {
      success: false,
      message: "请求超时，请稍后重试",
      error: "请求超时",
    };
  }

  return {
    success: false,
    message: "网络错误，请稍后重试",
    error: error.message,
  };
}

/**
 * 认证错误处理
 */
export function handleAuthError(status: number, response: any): ErrorResponse {
  switch (status) {
    case 401:
      return {
        success: false,
        message: "登录已过期，请重新登录",
        error: "未授权",
      };
    case 403:
      return {
        success: false,
        message: "权限不足，无法访问此资源",
        error: "权限不足",
      };
    case 422:
      return parseApiError(response);
    default:
      return parseApiError(response);
  }
}

/**
 * 获取用户友好的错误消息
 */
export function getUserFriendlyMessage(error: ApiError): string {
  const errorMessages: Record<string, string> = {
    // 验证错误
    "value_error.missing": "必填字段不能为空",
    "value_error.email": "请输入有效的邮箱地址",
    "value_error.str.min_length": "输入内容过短",
    "value_error.str.max_length": "输入内容过长",
    "type_error.none.not_allowed": "此字段不能为空",
    
    // 认证错误
    "invalid_credentials": "用户名或密码错误",
    "user_not_found": "用户不存在",
    "email_already_exists": "邮箱已被注册",
    "username_already_exists": "用户名已被使用",
    "invalid_token": "无效的令牌",
    "token_expired": "令牌已过期",
    "email_not_verified": "邮箱未验证",
    "account_disabled": "账户已被禁用",
    "account_locked": "账户已被锁定",
    
    // 密码错误
    "password_too_weak": "密码强度不够",
    "password_mismatch": "两次输入的密码不一致",
    "old_password_incorrect": "原密码错误",
  };

  return errorMessages[error.code || ""] || error.message;
}

/**
 * 格式化错误消息用于显示
 */
export function formatErrorMessage(errorResponse: ErrorResponse): string {
  if (errorResponse.errors && errorResponse.errors.length > 0) {
    // 如果有多个错误，显示第一个用户友好的错误
    const firstError = errorResponse.errors[0];
    return getUserFriendlyMessage(firstError);
  }

  return errorResponse.message;
}
