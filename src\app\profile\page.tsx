"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { getMyProfile, getProfileStatistics } from "@/services/profileService";
import { ProfileCard } from "@/components/profile/ProfileCard";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import { AuthGuard } from "@/components/AuthGuard";
import { 
  User, 
  Settings, 
  Award, 
  MessageSquare, 
  HelpCircle,
  TrendingUp,
  Calendar,
  Mail,
  AlertCircle,
  RefreshCw
} from "lucide-react";

interface ProfileStats {
  total_help_requests: number;
  total_help_answers: number;
  accepted_answers: number;
  total_points: number;
  current_title: string;
}

export default function ProfilePage() {
  const router = useRouter();
  const { user } = useAuth();
  
  const [profile, setProfile] = useState(null);
  const [stats, setStats] = useState<ProfileStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfileData();
  }, []);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [profileResult, statsResult] = await Promise.all([
        getMyProfile(),
        getProfileStatistics()
      ]);
      
      if (profileResult.success && profileResult.data) {
        setProfile(profileResult.data);
      } else {
        setError(profileResult.message || "获取个人信息失败");
      }

      if (statsResult.success && statsResult.data) {
        setStats(statsResult.data);
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleEditProfile = () => {
    router.push("/profile/edit");
  };

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    description,
    onClick 
  }: {
    title: string;
    value: string | number;
    icon: any;
    description?: string;
    onClick?: () => void;
  }) => (
    <Card 
      className={`${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <AuthGuard>
        <PageContainer>
          <div className="min-h-[60vh] flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        </PageContainer>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <PageContainer>
        <div className="py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-display font-bold">个人资料</h1>
            <p className="text-muted-foreground mt-2">
              查看和管理您的个人信息
            </p>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{error}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadProfileData}
                  className="ml-4"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重试
                </Button>
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 左侧：个人信息卡片 */}
            <div className="lg:col-span-2">
              <ProfileCard 
                onEditClick={handleEditProfile}
                showEditButton={true}
              />
            </div>

            {/* 右侧：快捷操作 */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">快捷操作</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/edit")}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    编辑资料
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/points")}
                  >
                    <Award className="h-4 w-4 mr-2" />
                    积分历史
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/help-requests")}
                  >
                    <HelpCircle className="h-4 w-4 mr-2" />
                    我的求助
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => router.push("/profile/help-answers")}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    我的回答
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* 统计信息 */}
          {stats && (
            <div className="mt-8">
              <h2 className="text-xl font-semibold mb-4">活动统计</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  title="总积分"
                  value={stats.total_points}
                  icon={Award}
                  description={`当前等级：${stats.current_title}`}
                  onClick={() => router.push("/profile/points")}
                />
                
                <StatCard
                  title="发起求助"
                  value={stats.total_help_requests}
                  icon={HelpCircle}
                  description="累计发起的求助"
                  onClick={() => router.push("/profile/help-requests")}
                />
                
                <StatCard
                  title="提供回答"
                  value={stats.total_help_answers}
                  icon={MessageSquare}
                  description="累计提供的回答"
                  onClick={() => router.push("/profile/help-answers")}
                />
                
                <StatCard
                  title="被采纳回答"
                  value={stats.accepted_answers}
                  icon={TrendingUp}
                  description={`采纳率：${stats.total_help_answers > 0 ? Math.round((stats.accepted_answers / stats.total_help_answers) * 100) : 0}%`}
                />
              </div>
            </div>
          )}

          {/* 最近活动 */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  最近活动
                </CardTitle>
                <CardDescription>
                  查看您最近的活动记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>暂无最近活动记录</p>
                  <p className="text-sm mt-1">开始使用平台功能来查看活动记录</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    </AuthGuard>
  );
}
