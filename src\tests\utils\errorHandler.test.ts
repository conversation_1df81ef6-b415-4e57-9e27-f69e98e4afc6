/**
 * 错误处理工具测试用例
 */

import { describe, it, expect } from 'vitest';
import {
  parseApiError,
  handleNetworkError,
  handleAuthError,
  getUserFriendlyMessage,
  formatErrorMessage,
  type ApiError,
} from '../../utils/errorHandler';

describe('ErrorHandler', () => {
  describe('parseApiError', () => {
    it('应该解析FastAPI验证错误', () => {
      const response = {
        detail: [
          {
            type: 'value_error.missing',
            msg: 'field required',
            loc: ['username'],
          },
          {
            type: 'value_error.email',
            msg: 'invalid email format',
            loc: ['email'],
          },
        ],
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe('请求参数验证失败');
      expect(result.errors).toHaveLength(2);
      expect(result.errors![0]).toEqual({
        code: 'value_error.missing',
        message: 'field required',
        field: 'username',
      });
    });

    it('应该解析单个错误信息', () => {
      const response = {
        detail: '用户名或密码错误',
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe('用户名或密码错误');
      expect(result.error).toBe('用户名或密码错误');
    });

    it('应该解析自定义错误格式', () => {
      const response = {
        message: '操作失败',
        error: 'OPERATION_FAILED',
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe('操作失败');
      expect(result.error).toBe('OPERATION_FAILED');
    });

    it('应该处理未知错误格式', () => {
      const response = {
        unknown: 'unknown error',
      };

      const result = parseApiError(response);

      expect(result.success).toBe(false);
      expect(result.message).toBe('请求失败');
      expect(result.error).toBe('未知错误');
    });
  });

  describe('handleNetworkError', () => {
    it('应该处理网络连接错误', () => {
      const error = new TypeError('Failed to fetch');

      const result = handleNetworkError(error);

      expect(result.success).toBe(false);
      expect(result.message).toBe('网络连接失败，请检查网络连接');
      expect(result.error).toBe('网络错误');
    });

    it('应该处理请求超时错误', () => {
      const error = new Error('Request timeout');
      error.name = 'AbortError';

      const result = handleNetworkError(error);

      expect(result.success).toBe(false);
      expect(result.message).toBe('请求超时，请稍后重试');
      expect(result.error).toBe('请求超时');
    });

    it('应该处理一般网络错误', () => {
      const error = new Error('Network error');

      const result = handleNetworkError(error);

      expect(result.success).toBe(false);
      expect(result.message).toBe('网络错误，请稍后重试');
      expect(result.error).toBe('Network error');
    });
  });

  describe('handleAuthError', () => {
    it('应该处理401未授权错误', () => {
      const result = handleAuthError(401, {});

      expect(result.success).toBe(false);
      expect(result.message).toBe('登录已过期，请重新登录');
      expect(result.error).toBe('未授权');
    });

    it('应该处理403权限不足错误', () => {
      const result = handleAuthError(403, {});

      expect(result.success).toBe(false);
      expect(result.message).toBe('权限不足，无法访问此资源');
      expect(result.error).toBe('权限不足');
    });

    it('应该处理422验证错误', () => {
      const response = {
        detail: [
          {
            type: 'value_error.missing',
            msg: 'field required',
            loc: ['username'],
          },
        ],
      };

      const result = handleAuthError(422, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe('请求参数验证失败');
      expect(result.errors).toHaveLength(1);
    });

    it('应该处理其他状态码', () => {
      const response = {
        detail: '服务器内部错误',
      };

      const result = handleAuthError(500, response);

      expect(result.success).toBe(false);
      expect(result.message).toBe('服务器内部错误');
    });
  });

  describe('getUserFriendlyMessage', () => {
    it('应该返回用户友好的验证错误消息', () => {
      const error: ApiError = {
        code: 'value_error.missing',
        message: 'field required',
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe('必填字段不能为空');
    });

    it('应该返回用户友好的认证错误消息', () => {
      const error: ApiError = {
        code: 'invalid_credentials',
        message: 'Invalid username or password',
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe('用户名或密码错误');
    });

    it('应该在没有匹配的错误码时返回原始消息', () => {
      const error: ApiError = {
        code: 'unknown_error',
        message: 'Unknown error occurred',
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe('Unknown error occurred');
    });

    it('应该在没有错误码时返回原始消息', () => {
      const error: ApiError = {
        message: 'Some error message',
      };

      const result = getUserFriendlyMessage(error);

      expect(result).toBe('Some error message');
    });
  });

  describe('formatErrorMessage', () => {
    it('应该格式化包含多个错误的响应', () => {
      const errorResponse = {
        success: false as const,
        message: '请求参数验证失败',
        errors: [
          {
            code: 'value_error.missing',
            message: 'field required',
            field: 'username',
          },
          {
            code: 'value_error.email',
            message: 'invalid email format',
            field: 'email',
          },
        ],
      };

      const result = formatErrorMessage(errorResponse);

      expect(result).toBe('必填字段不能为空');
    });

    it('应该格式化单个错误的响应', () => {
      const errorResponse = {
        success: false as const,
        message: '用户名或密码错误',
      };

      const result = formatErrorMessage(errorResponse);

      expect(result).toBe('用户名或密码错误');
    });

    it('应该处理空错误数组', () => {
      const errorResponse = {
        success: false as const,
        message: '操作失败',
        errors: [],
      };

      const result = formatErrorMessage(errorResponse);

      expect(result).toBe('操作失败');
    });
  });
});
