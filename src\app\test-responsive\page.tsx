"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Sun, 
  Moon,
  AlertCircle,
  CheckCircle,
  Info
} from "lucide-react";

export default function ResponsiveTestPage() {
  return (
    <PageContainer>
      <div className="py-8">
        {/* 面包屑导航 */}
        <div className="mb-6">
          <Breadcrumb
            items={[
              { label: "测试", href: "/test" },
              { label: "响应式测试" },
            ]}
          />
        </div>

        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-display font-bold">响应式设计测试</h1>
          <p className="text-muted-foreground mt-2">
            测试所有组件在不同屏幕尺寸和主题下的显示效果
          </p>
        </div>

        {/* 断点指示器 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>当前断点</CardTitle>
            <CardDescription>
              显示当前屏幕对应的Tailwind断点
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 xs:block sm:hidden">
                <Smartphone className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium">XS (320px+)</span>
              </div>
              <div className="hidden sm:flex md:hidden items-center space-x-2">
                <Smartphone className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium">SM (640px+)</span>
              </div>
              <div className="hidden md:flex lg:hidden items-center space-x-2">
                <Tablet className="h-5 w-5 text-orange-600" />
                <span className="text-sm font-medium">MD (768px+)</span>
              </div>
              <div className="hidden lg:flex xl:hidden items-center space-x-2">
                <Monitor className="h-5 w-5 text-purple-600" />
                <span className="text-sm font-medium">LG (1024px+)</span>
              </div>
              <div className="hidden xl:flex items-center space-x-2">
                <Monitor className="h-5 w-5 text-red-600" />
                <span className="text-sm font-medium">XL (1280px+)</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 网格布局测试 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">网格布局测试</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-4">
                  <h3 className="font-medium mb-2">卡片 {i + 1}</h3>
                  <p className="text-sm text-muted-foreground">
                    这是一个测试卡片，用于验证网格布局在不同屏幕尺寸下的表现。
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 表单组件测试 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">表单组件测试</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>登录表单</CardTitle>
                <CardDescription>
                  测试表单组件的响应式布局
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">用户名</Label>
                  <Input
                    id="username"
                    placeholder="请输入用户名"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">密码</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="请输入密码"
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button className="flex-1">登录</Button>
                  <Button variant="outline" className="flex-1">取消</Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>设置表单</CardTitle>
                <CardDescription>
                  测试复杂表单的响应式布局
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">名字</Label>
                    <Input id="firstName" placeholder="名字" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">姓氏</Label>
                    <Input id="lastName" placeholder="姓氏" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">邮箱</Label>
                  <Input id="email" type="email" placeholder="邮箱地址" />
                </div>
                <Button className="w-full sm:w-auto">保存设置</Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 警告组件测试 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">警告组件测试</h2>
          <div className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                这是一个信息提示，用于显示一般性信息。在移动设备上应该正确换行和显示。
              </AlertDescription>
            </Alert>

            <Alert variant="success">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                这是一个成功提示，用于显示操作成功的信息。文本应该在小屏幕上正确换行。
              </AlertDescription>
            </Alert>

            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                这是一个错误提示，用于显示错误信息。即使在最小的屏幕上也应该清晰可读。
              </AlertDescription>
            </Alert>
          </div>
        </div>

        {/* 按钮组测试 */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">按钮组测试</h2>
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button>主要按钮</Button>
                  <Button variant="outline">次要按钮</Button>
                  <Button variant="ghost">幽灵按钮</Button>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  <Button size="sm">小按钮</Button>
                  <Button>默认按钮</Button>
                  <Button size="lg">大按钮</Button>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                  <Button className="w-full">全宽按钮 1</Button>
                  <Button className="w-full" variant="outline">全宽按钮 2</Button>
                  <Button className="w-full" variant="ghost">全宽按钮 3</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 主题测试说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Sun className="h-5 w-5 mr-2 dark:hidden" />
              <Moon className="h-5 w-5 mr-2 hidden dark:block" />
              主题测试
            </CardTitle>
            <CardDescription>
              使用导航栏中的主题切换按钮测试深色/浅色模式
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>• 所有组件应该在两种主题下都清晰可见</p>
              <p>• 颜色对比度应该符合可访问性标准</p>
              <p>• 主题切换应该平滑过渡</p>
              <p>• 图标和文本应该适配当前主题</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
