import {
  parseApiError,
  handleNetworkError,
  handleAuthError,
  formatErrorMessage,
  type ErrorResponse,
} from "../utils/errorHandler";
import { tokenManager } from "../utils/tokenManager";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

// 用户认证相关的类型定义（基于后端API规范）
export interface LoginRequest {
  username: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: UserProfile;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
  agree_terms: boolean;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  new_password: string;
  confirm_password: string;
}

export interface TokenRefreshRequest {
  refresh_token: string;
}

export interface TokenRefreshResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  nickname?: string | null;
  avatar?: string | null;
  status: string;
  email_verified: boolean;
  role: {
    id: string;
    name: string;
    display_name?: string;
    description?: string;
    permissions?: any;
  };
  points: number;
  title: string;
  last_login_at?: string | null;
  created_at: string;
}

// 兼容旧版本的User类型
export interface User extends UserProfile {
  last_login?: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user?: User;
    token?: string;
    expires_at?: string;
  };
  error?: string;
}

export interface UserListResponse {
  success: boolean;
  data: {
    users: User[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
  error?: string;
}

/**
 * 用户登录
 */
export async function login(credentials: LoginRequest): Promise<AuthResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleAuthError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data: LoginResponse = await response.json();

    // 计算过期时间
    const expiresAt = new Date(
      Date.now() + data.expires_in * 1000
    ).toISOString();

    // 保存认证信息到localStorage
    localStorage.setItem("auth_token", data.access_token);
    localStorage.setItem("auth_refresh_token", data.refresh_token);
    localStorage.setItem("auth_expires", expiresAt);

    // 缓存用户信息
    setCachedUserInfo(data.user);

    // 重置Token管理器以开始自动刷新
    if (typeof window !== "undefined") {
      tokenManager.reset();
    }

    return {
      success: true,
      message: "登录成功",
      data: {
        token: data.access_token,
        user: data.user,
        expires_at: expiresAt,
      },
    };
  } catch (error) {
    console.error("登录请求失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 用户注册
 */
export async function register(
  userData: RegisterRequest
): Promise<AuthResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleAuthError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: data.message || "注册成功",
      data: data.data,
    };
  } catch (error) {
    console.error("注册请求失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 忘记密码
 */
export async function forgotPassword(
  request: ForgotPasswordRequest
): Promise<AuthResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/forgot-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "发送重置邮件失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "重置邮件已发送",
      data: data.data,
    };
  } catch (error) {
    console.error("忘记密码请求失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 重置密码
 */
export async function resetPassword(
  request: ResetPasswordRequest
): Promise<AuthResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/reset-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "重置密码失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "密码重置成功",
      data: data.data,
    };
  } catch (error) {
    console.error("重置密码请求失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 用户登出
 */
export async function logout(): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");

    // 清除本地存储的函数
    const clearLocalStorage = () => {
      localStorage.removeItem("auth_token");
      localStorage.removeItem("auth_refresh_token");
      localStorage.removeItem("auth_expires");
      setCachedUserInfo(null);
      currentUserPromise = null; // 清除请求缓存

      // 停止Token管理器
      if (typeof window !== "undefined") {
        tokenManager.stop();
      }
    };

    if (token) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/logout`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });

        // 无论后端响应如何，都清除本地存储
        clearLocalStorage();

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          return {
            success: false,
            message: errorData.detail || errorData.message || "登出失败",
          };
        }

        return {
          success: true,
          message: "登出成功",
        };
      } catch (error) {
        // 网络错误时也要清除本地存储
        clearLocalStorage();
        console.error("登出请求失败:", error);
        return {
          success: true, // 本地清除成功就算成功
          message: "已清除本地登录状态",
        };
      }
    } else {
      // 如果没有token，直接清除本地存储
      clearLocalStorage();
      return {
        success: true,
        message: "已清除本地登录状态",
      };
    }
  } catch (error) {
    console.error("登出过程出错:", error);
    return {
      success: false,
      message: "登出失败",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 刷新访问令牌
 */
export async function refreshToken(): Promise<AuthResponse> {
  try {
    const refreshToken = localStorage.getItem("auth_refresh_token");

    if (!refreshToken) {
      return {
        success: false,
        message: "没有刷新令牌，请重新登录",
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      // 刷新失败，清除所有认证信息
      localStorage.removeItem("auth_token");
      localStorage.removeItem("auth_refresh_token");
      localStorage.removeItem("auth_expires");
      setCachedUserInfo(null);
      currentUserPromise = null;

      return {
        success: false,
        message:
          errorData.detail || errorData.message || "令牌刷新失败，请重新登录",
      };
    }

    const data: TokenRefreshResponse = await response.json();

    // 计算新的过期时间
    const expiresAt = new Date(
      Date.now() + data.expires_in * 1000
    ).toISOString();

    // 更新本地存储
    localStorage.setItem("auth_token", data.access_token);
    localStorage.setItem("auth_expires", expiresAt);

    return {
      success: true,
      message: "令牌刷新成功",
      data: {
        token: data.access_token,
        expires_at: expiresAt,
      },
    };
  } catch (error) {
    console.error("令牌刷新失败:", error);
    // 刷新失败，清除所有认证信息
    localStorage.removeItem("auth_token");
    localStorage.removeItem("auth_refresh_token");
    localStorage.removeItem("auth_expires");
    setCachedUserInfo(null);
    currentUserPromise = null;

    return {
      success: false,
      message: "网络错误，请重新登录",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 检查用户是否已登录
 */
export function isAuthenticated(): boolean {
  if (typeof window === "undefined") {
    return false;
  }

  const token = localStorage.getItem("auth_token");
  const expires = localStorage.getItem("auth_expires");

  if (!token) return false;

  if (expires) {
    const expiresDate = new Date(expires);
    const now = new Date();
    if (expiresDate <= now) {
      // Token已过期，清除本地存储和用户缓存
      localStorage.removeItem("auth_token");
      localStorage.removeItem("auth_refresh_token");
      localStorage.removeItem("auth_expires");
      setCachedUserInfo(null);
      currentUserPromise = null; // 清除请求缓存
      return false;
    }
  }

  return true;
}

/**
 * 检查Token是否即将过期（5分钟内）
 */
export function isTokenExpiringSoon(): boolean {
  if (typeof window === "undefined") {
    return false;
  }

  const expires = localStorage.getItem("auth_expires");
  if (!expires) return false;

  const expiresDate = new Date(expires);
  const now = new Date();
  const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

  return expiresDate <= fiveMinutesFromNow;
}

// 防止重复请求的Promise缓存
let currentUserPromise: Promise<User | null> | null = null;

/**
 * 获取当前用户信息
 * 使用正确的 /api/auth/profile 接口
 * 带有请求去重机制，防止同时发起多个相同请求
 */
export async function getCurrentUser(): Promise<User | null> {
  // 如果已经有正在进行的请求，直接返回该Promise
  if (currentUserPromise) {
    console.log("👤 getCurrentUser: 等待正在进行的请求...");
    return currentUserPromise;
  }

  // 创建新的请求Promise
  currentUserPromise = (async (): Promise<User | null> => {
    try {
      const token = localStorage.getItem("auth_token");

      if (!token) {
        console.log("👤 getCurrentUser: 没有token");
        return null;
      }

      console.log("👤 getCurrentUser: 发送请求获取用户信息", {
        apiUrl: `${API_BASE_URL}/api/auth/profile`,
        tokenLength: token.length,
        tokenPrefix: token.substring(0, 10) + "...",
      });

      const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log("👤 getCurrentUser: 收到响应", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      if (!response.ok) {
        console.log("👤 getCurrentUser: 请求失败，状态码:", response.status);

        // 尝试读取错误响应
        try {
          const errorData = await response.json();
          console.log("👤 getCurrentUser: 错误响应数据:", errorData);
        } catch {
          console.log("👤 getCurrentUser: 无法解析错误响应");
        }

        if (response.status === 401) {
          // Token无效，清除本地存储
          console.log("👤 getCurrentUser: Token无效，清除本地存储");
          localStorage.removeItem("auth_token");
          localStorage.removeItem("auth_expires");
          setCachedUserInfo(null);
          currentUserPromise = null; // 清除请求缓存
        }
        return null;
      }

      const data = await response.json();
      console.log(
        "👤 getCurrentUser: 原始响应数据:",
        JSON.stringify(data, null, 2)
      );

      // 根据API规范，profile接口直接返回用户信息
      const user = data;
      console.log("👤 getCurrentUser: 提取的用户数据:", user);

      // 后端直接返回角色对象，无需转换

      console.log("👤 getCurrentUser: 最终用户信息:", {
        username: user?.username,
        role: user?.role,
        id: user?.id,
        email: user?.email,
      });

      // 缓存用户信息
      setCachedUserInfo(user);

      return user;
    } catch (error) {
      console.error("👤 getCurrentUser: 获取用户信息失败:", error);
      return null;
    } finally {
      // 清除Promise缓存，允许后续请求
      currentUserPromise = null;
    }
  })();

  return currentUserPromise;
}

/**
 * 设置用户信息缓存（在登录成功时调用）
 */
export function setCachedUserInfo(user: User | null) {
  if (user) {
    localStorage.setItem("auth_user", JSON.stringify(user));
  } else {
    localStorage.removeItem("auth_user");
  }
}

/**
 * 获取用户列表（管理员功能）
 */
export async function getUserList(
  page: number = 1,
  limit: number = 20,
  search?: string,
  role?: string,
  status?: string
): Promise<UserListResponse> {
  try {
    const token = localStorage.getItem("auth_token");

    if (!token) {
      return {
        success: false,
        data: { users: [], total: 0, page: 1, limit: 20, total_pages: 0 },
        error: "未登录",
      };
    }

    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (search) params.append("search", search);
    if (role) params.append("role", role);
    if (status) params.append("status", status);

    const response = await fetch(`${API_BASE_URL}/api/admin/users?${params}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        data: { users: [], total: 0, page: 1, limit: 20, total_pages: 0 },
        message: data.message || "获取用户列表失败",
        error: data.error,
      };
    }

    // 后端直接返回角色对象，无需转换

    return {
      success: true,
      data: data.data,
      message: data.message,
    };
  } catch (error) {
    return {
      success: false,
      data: { users: [], total: 0, page: 1, limit: 20, total_pages: 0 },
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 创建新用户（管理员功能）
 */
export async function createUser(
  userData: Omit<RegisterRequest, "confirm_password" | "agree_terms"> & {
    role: string;
  }
): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");

    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/admin/users`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "创建用户失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "用户创建成功",
      data: data.data,
    };
  } catch (error) {
    console.error("创建用户失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 更新用户信息（管理员功能）
 */
export async function updateUser(
  userId: number,
  userData: Partial<User>
): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");

    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(userData),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "更新用户失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "用户更新成功",
      data: data.data,
    };
  } catch (error) {
    console.error("更新用户失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 删除用户（管理员功能）
 */
export async function deleteUser(userId: number): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");

    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "删除用户失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "用户删除成功",
    };
  } catch (error) {
    console.error("删除用户失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 重置用户密码（管理员功能）
 */
export async function resetUserPassword(
  userId: number,
  newPassword: string
): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");

    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/users/${userId}/reset-password`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ new_password: newPassword }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "重置密码失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "密码重置成功",
    };
  } catch (error) {
    console.error("重置用户密码失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 冻结用户
 */
export async function freezeUser(userId: number): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/users/${userId}/freeze`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "冻结用户失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "用户冻结成功",
    };
  } catch (error) {
    console.error("冻结用户失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 解冻用户
 */
export async function unfreezeUser(userId: number): Promise<AuthResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录",
        error: "需要管理员权限",
      };
    }

    const response = await fetch(
      `${API_BASE_URL}/api/admin/users/${userId}/unfreeze`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.message || "解冻用户失败",
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message || "用户解冻成功",
    };
  } catch (error) {
    console.error("解冻用户失败:", error);
    return {
      success: false,
      message: "网络错误，请稍后重试",
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}
