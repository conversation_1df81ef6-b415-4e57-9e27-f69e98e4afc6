"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/components/ToastProvider";
import { register, RegisterRequest } from "@/services/authService";
import PageHeader from "@/components/PageHeader";
import { isRegistrationEnabled } from "@/config/features";

export default function RegisterPage() {
  const router = useRouter();
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // 检查注册功能是否启用
  useEffect(() => {
    if (!isRegistrationEnabled()) {
      showToast("注册功能暂时关闭", "error");
      router.push("/login");
    }
  }, [router, showToast]);
  const [formData, setFormData] = useState<RegisterRequest>({
    username: "",
    email: "",
    password: "",
    confirm_password: "",
    agree_terms: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      showToast("请输入用户名", "error");
      return false;
    }

    if (formData.username.length < 3) {
      showToast("用户名至少需要3个字符", "error");
      return false;
    }

    if (!formData.email.trim()) {
      showToast("请输入邮箱地址", "error");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      showToast("请输入有效的邮箱地址", "error");
      return false;
    }

    if (!formData.password.trim()) {
      showToast("请输入密码", "error");
      return false;
    }

    if (formData.password.length < 6) {
      showToast("密码至少需要6个字符", "error");
      return false;
    }

    if (formData.password !== formData.confirm_password) {
      showToast("两次输入的密码不一致", "error");
      return false;
    }

    if (!formData.agree_terms) {
      showToast("请同意用户协议和隐私政策", "error");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await register(formData);

      if (result.success) {
        showToast(result.message, "success");
        // 注册成功后跳转到登录页面
        router.push("/login?message=注册成功，请登录");
      } else {
        showToast(result.message, "error");
      }
    } catch {
      showToast("注册失败，请稍后重试", "error");
    } finally {
      setIsLoading(false);
    }
  };

  // 如果注册功能未启用，显示加载状态（实际上会重定向）
  if (!isRegistrationEnabled()) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-button-background mx-auto mb-4"></div>
          <p className="text-secondary-text">正在跳转...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <PageHeader title="用户注册" description="创建您的账户以享受完整功能" />

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-card-background rounded-lg shadow-lg p-8 border border-border-color">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-foreground mb-2">
                创建账户
              </h1>
              <p className="text-secondary-text">填写以下信息完成注册</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="username"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  用户名
                </label>
                <Input
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="请输入用户名（至少3个字符）"
                  required
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  邮箱地址
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="请输入邮箱地址"
                  required
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  密码
                </label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="请输入密码（至少6个字符）"
                  required
                  className="w-full"
                />
              </div>

              <div>
                <label
                  htmlFor="confirm_password"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  确认密码
                </label>
                <Input
                  id="confirm_password"
                  name="confirm_password"
                  type="password"
                  value={formData.confirm_password}
                  onChange={handleInputChange}
                  placeholder="请再次输入密码"
                  required
                  className="w-full"
                />
              </div>

              <div className="flex items-start">
                <input
                  id="agree_terms"
                  name="agree_terms"
                  type="checkbox"
                  checked={formData.agree_terms}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-button-background focus:ring-button-background border-border-color rounded mt-1"
                  required
                />
                <label
                  htmlFor="agree_terms"
                  className="ml-2 block text-sm text-secondary-text"
                >
                  我已阅读并同意{" "}
                  <Link
                    href="/terms"
                    className="text-link-color hover:text-button-hover transition-colors"
                    target="_blank"
                  >
                    用户协议
                  </Link>{" "}
                  和{" "}
                  <Link
                    href="/privacy"
                    className="text-link-color hover:text-button-hover transition-colors"
                    target="_blank"
                  >
                    隐私政策
                  </Link>
                </label>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? "注册中..." : "注册"}
              </Button>
            </form>

            <div className="mt-8 text-center">
              <p className="text-secondary-text">
                已有账户？{" "}
                <Link
                  href="/login"
                  className="text-link-color hover:text-button-hover font-medium transition-colors"
                >
                  立即登录
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
