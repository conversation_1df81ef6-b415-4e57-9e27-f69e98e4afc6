﻿{"openapi":"3.1.0","info":{"title":"ç½çæç´¢API","version":"0.1.0"},"paths":{"/":{"get":{"summary":"Root","operationId":"root__get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}}}}},"/{key_file_name}":{"get":{"summary":"Get Indexnow Key","description":"æç®¡ IndexNow API å¯é¥æä»¶ã\nå½æç´¢å¼æè®¿é® /<your_api_key_file>.txt æ¶ï¼\næ­¤ç«¯ç¹ä¼è¿åå¯é¥åå®¹ä»¥éªè¯ååæææã","operationId":"get_indexnow_key__key_file_name__get","parameters":[{"name":"key_file_name","in":"path","required":true,"schema":{"type":"string","title":"Key File Name"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/auth/register":{"post":{"tags":["è®¤è¯"],"summary":"ç¨æ·æ³¨å","description":"ç¨æ·æ³¨åï¼éè¦é®ç®±éªè¯","operationId":"register_api_auth_register_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserRegisterRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/auth/login":{"post":{"tags":["è®¤è¯"],"summary":"ç¨æ·ç»å½","description":"ç¨æ·ç»å½ï¼è¿åè®¿é®ä»¤ç","operationId":"login_api_auth_login_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserLoginRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserLoginResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/auth/logout":{"post":{"tags":["è®¤è¯"],"summary":"ç¨æ·ç»åº","description":"ç¨æ·ç»åºï¼ä½¿å·æ°ä»¤çå¤±æ","operationId":"logout_api_auth_logout_post","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/auth/refresh":{"post":{"tags":["è®¤è¯"],"summary":"å·æ°ä»¤ç","description":"ä½¿ç¨å·æ°ä»¤çè·åæ°çè®¿é®ä»¤ç","operationId":"refresh_token_api_auth_refresh_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/TokenRefreshRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TokenRefreshResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/auth/verify-email":{"post":{"tags":["è®¤è¯"],"summary":"éªè¯é®ç®±","description":"éªè¯ç¨æ·é®ç®±å°å","operationId":"verify_email_api_auth_verify_email_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/EmailVerificationRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/auth/forgot-password":{"post":{"tags":["è®¤è¯"],"summary":"å¿è®°å¯ç ","description":"åéå¯ç éç½®é®ä»¶","operationId":"forgot_password_api_auth_forgot_password_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ForgotPasswordRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/auth/reset-password":{"post":{"tags":["è®¤è¯"],"summary":"éç½®å¯ç ","description":"ä½¿ç¨éç½®ä»¤çéç½®å¯ç ","operationId":"reset_password_api_auth_reset_password_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResetPasswordRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/auth/profile":{"get":{"tags":["è®¤è¯"],"summary":"è·åç¨æ·èµæ","description":"è·åå½åç¨æ·çä¸ªäººèµæ","operationId":"get_profile_api_auth_profile_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserProfileResponse"}}}}},"security":[{"HTTPBearer":[]}]},"put":{"tags":["è®¤è¯"],"summary":"æ´æ°ç¨æ·èµæ","description":"æ´æ°å½åç¨æ·çä¸ªäººèµæ","operationId":"update_profile_api_auth_profile_put","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/app__models__auth_models__UserProfileUpdateRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserProfileResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/auth/change-password":{"post":{"tags":["è®¤è¯"],"summary":"ä¿®æ¹å¯ç ","description":"ä¿®æ¹å½åç¨æ·çå¯ç ","operationId":"change_password_api_auth_change_password_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/app__models__auth_models__ChangePasswordRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/admin/resources/stats":{"get":{"tags":["ç®¡çå-ç¨æ·ç®¡ç","ç®¡çå-èµæºç®¡ç","ç®¡çå-èµæºç®¡ç"],"summary":"ç®¡çåèµæºç»è®¡","description":"è·åè¯¦ç»çèµæºç»è®¡ä¿¡æ¯ï¼æ©å±ç°æresource_stats","operationId":"get_admin_resource_stats_api_admin_resources_stats_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}}},"security":[{"HTTPBearer":[]}]}},"/api/admin/resources":{"get":{"tags":["ç®¡çå-ç¨æ·ç®¡ç","ç®¡çå-èµæºç®¡ç","ç®¡çå-èµæºç®¡ç"],"summary":"ç®¡çåèµæºåè¡¨","description":"è·åèµæºåè¡¨ï¼æ¯æé«çº§ç­éåæç´¢ï¼å¤ç¨cached_resourcesé»è¾","operationId":"get_admin_resources_api_admin_resources_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"keyword","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æç´¢å³é®è¯ï¼æ é¢/ä½è/resource_keyï¼","title":"Keyword"},"description":"æç´¢å³é®è¯ï¼æ é¢/ä½è/resource_keyï¼"},{"name":"pan_type","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"ç½çç±»åç­é: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç","title":"Pan Type"},"description":"ç½çç±»åç­é: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç"},{"name":"file_type","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æä»¶ç±»åç­é: video/audio/image/document/archive/application","title":"File Type"},"description":"æä»¶ç±»åç­é: video/audio/image/document/archive/application"},{"name":"status","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"èµæºç¶æç­é: valid/invalid/unknown","title":"Status"},"description":"èµæºç¶æç­é: valid/invalid/unknown"},{"name":"is_mine","in":"query","required":false,"schema":{"anyOf":[{"type":"boolean"},{"type":"null"}],"description":"æ¯å¦æ¬äººä¸ä¼ ç­é","title":"Is Mine"},"description":"æ¯å¦æ¬äººä¸ä¼ ç­é"},{"name":"page","in":"query","required":false,"schema":{"type":"integer","minimum":1,"description":"é¡µç ","default":1,"title":"Page"},"description":"é¡µç "},{"name":"size","in":"query","required":false,"schema":{"type":"integer","maximum":100,"minimum":1,"description":"æ¯é¡µæ°é","default":20,"title":"Size"},"description":"æ¯é¡µæ°é"},{"name":"sort_by","in":"query","required":false,"schema":{"type":"string","description":"æåºå­æ®µ: updated_at/created_at/access_count/title","default":"updated_at","title":"Sort By"},"description":"æåºå­æ®µ: updated_at/created_at/access_count/title"},{"name":"sort_order","in":"query","required":false,"schema":{"type":"string","description":"æåºæ¹å: asc/desc","default":"desc","title":"Sort Order"},"description":"æåºæ¹å: asc/desc"},{"name":"time_filter","in":"query","required":false,"schema":{"allOf":[{"$ref":"#/components/schemas/TimeFilter"}],"description":"æ¶é´èå´ç­é","default":"all","title":"Time Filter"},"description":"æ¶é´èå´ç­é"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/resources/{resource_key}":{"get":{"tags":["ç®¡çå-ç¨æ·ç®¡ç","ç®¡çå-èµæºç®¡ç","ç®¡çå-èµæºç®¡ç"],"summary":"ç®¡çåèµæºè¯¦æ","description":"è·ååä¸ªèµæºçè¯¦ç»ä¿¡æ¯ï¼åå«ç®¡çåä¸ç¨ä¿¡æ¯","operationId":"get_admin_resource_detail_api_admin_resources__resource_key__get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"resource_key","in":"path","required":true,"schema":{"type":"string","title":"Resource Key"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/resources/{resource_id}":{"delete":{"tags":["ç®¡çå-ç¨æ·ç®¡ç","ç®¡çå-èµæºç®¡ç","ç®¡çå-èµæºç®¡ç"],"summary":"å é¤åä¸ªèµæº","description":"å é¤æå®çèµæºåå¶ç¸å³åé¦","operationId":"delete_admin_resource_api_admin_resources__resource_id__delete","security":[{"HTTPBearer":[]}],"parameters":[{"name":"resource_id","in":"path","required":true,"schema":{"type":"integer","title":"Resource Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/resources/batch-delete":{"post":{"tags":["ç®¡çå-ç¨æ·ç®¡ç","ç®¡çå-èµæºç®¡ç","ç®¡çå-èµæºç®¡ç"],"summary":"æ¹éå é¤èµæº","description":"æ¹éå é¤å¤ä¸ªèµæºåå¶ç¸å³åé¦","operationId":"batch_delete_resources_api_admin_resources_batch_delete_post","requestBody":{"content":{"application/json":{"schema":{"items":{"type":"integer"},"type":"array","title":"Resource Ids"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/admin/users":{"get":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"è·åç¨æ·åè¡¨","description":"åé¡µè·åç¨æ·åè¡¨ï¼æ¯ææç´¢åç­é","operationId":"get_users_api_admin_users_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"page","in":"query","required":false,"schema":{"type":"integer","minimum":1,"description":"é¡µç ","default":1,"title":"Page"},"description":"é¡µç "},{"name":"size","in":"query","required":false,"schema":{"type":"integer","maximum":100,"minimum":1,"description":"æ¯é¡µæ°é","default":20,"title":"Size"},"description":"æ¯é¡µæ°é"},{"name":"keyword","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æç´¢å³é®è¯ï¼ç¨æ·å/é®ç®±ï¼","title":"Keyword"},"description":"æç´¢å³é®è¯ï¼ç¨æ·å/é®ç®±ï¼"},{"name":"status","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"ç¨æ·ç¶æç­é","title":"Status"},"description":"ç¨æ·ç¶æç­é"},{"name":"role_id","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"è§è²IDç­é","title":"Role Id"},"description":"è§è²IDç­é"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"type":"object","title":"Response Get Users Api Admin Users Get"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}},"post":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"åå»ºç¨æ·","description":"ç®¡çååå»ºæ°ç¨æ·","operationId":"create_user_api_admin_users_post","security":[{"HTTPBearer":[]}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserCreateRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserDetailResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/users/{user_id}":{"get":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"è·åç¨æ·è¯¦æ","description":"è·åæå®ç¨æ·çè¯¦ç»ä¿¡æ¯","operationId":"get_user_detail_api_admin_users__user_id__get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserDetailResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}},"put":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"æ´æ°ç¨æ·ä¿¡æ¯","description":"ç®¡çåæ´æ°ç¨æ·ä¿¡æ¯","operationId":"update_user_api_admin_users__user_id__put","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserUpdateRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserDetailResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}},"delete":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"å é¤ç¨æ·","description":"ç®¡çåå é¤ç¨æ·ï¼è½¯å é¤ï¼","operationId":"delete_user_api_admin_users__user_id__delete","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/users/{user_id}/freeze":{"post":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"å»ç»ç¨æ·","description":"å»ç»ç¨æ·è´¦æ·","operationId":"freeze_user_api_admin_users__user_id__freeze_post","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/users/{user_id}/unfreeze":{"post":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"è§£å»ç¨æ·","description":"è§£å»ç¨æ·è´¦æ·","operationId":"unfreeze_user_api_admin_users__user_id__unfreeze_post","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/users/{user_id}/reset-password":{"post":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"éç½®ç¨æ·å¯ç ","description":"ç®¡çåéç½®ç¨æ·å¯ç ","operationId":"reset_user_password_api_admin_users__user_id__reset_password_post","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/UserPasswordResetRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/users/{user_id}/sessions":{"get":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"è·åç¨æ·ä¼è¯åè¡¨","description":"è·åæå®ç¨æ·çæ´»è·ä¼è¯åè¡¨","operationId":"get_user_sessions_api_admin_users__user_id__sessions_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"type":"object","title":"Response Get User Sessions Api Admin Users  User Id  Sessions Get"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/users/{user_id}/sessions/{session_id}":{"delete":{"tags":["ç®¡çå-ç¨æ·ç®¡ç"],"summary":"å¼ºå¶ä¸çº¿ç¨æ·ä¼è¯","description":"ç®¡çåå¼ºå¶ä¸çº¿æå®ç¨æ·ä¼è¯","operationId":"force_logout_session_api_admin_users__user_id__sessions__session_id__delete","security":[{"HTTPBearer":[]}],"parameters":[{"name":"user_id","in":"path","required":true,"schema":{"type":"integer","title":"User Id"}},{"name":"session_id","in":"path","required":true,"schema":{"type":"integer","title":"Session Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/points/my":{"get":{"tags":["ç§¯åç³»ç»"],"summary":"è·åæçç§¯åä¿¡æ¯","description":"è·åå½åç¨æ·ç§¯åä¿¡æ¯","operationId":"get_my_points_api_points_my_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/points/history":{"get":{"tags":["ç§¯åç³»ç»"],"summary":"è·åæçç§¯ååå²è®°å½","description":"è·åå½åç¨æ·ç§¯ååå²","operationId":"get_points_history_api_points_history_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"page","in":"query","required":false,"schema":{"type":"integer","minimum":1,"description":"é¡µç ","default":1,"title":"Page"},"description":"é¡µç "},{"name":"size","in":"query","required":false,"schema":{"type":"integer","maximum":100,"minimum":1,"description":"æ¯é¡µæ°é","default":20,"title":"Size"},"description":"æ¯é¡µæ°é"},{"name":"transaction_type","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"äº¤æç±»åç­é","title":"Transaction Type"},"description":"äº¤æç±»åç­é"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/points/statistics":{"get":{"tags":["ç§¯åç³»ç»"],"summary":"è·åç§¯åç³»ç»ç»è®¡ä¿¡æ¯","description":"è·åç§¯åç³»ç»ç»è®¡ä¿¡æ¯ï¼ç®¡çåä¸ç¨ï¼","operationId":"get_points_statistics_api_points_statistics_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/points/adjust":{"post":{"tags":["ç§¯åç³»ç»"],"summary":"ç®¡çåè°æ´ç¨æ·ç§¯å","description":"ç®¡çåè°æ´ç¨æ·ç§¯å","operationId":"adjust_user_points_api_points_adjust_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PointsAdjustRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/points/leaderboard":{"get":{"tags":["ç§¯åç³»ç»"],"summary":"è·åç§¯åæè¡æ¦","description":"è·åç§¯åæè¡æ¦","operationId":"get_points_leaderboard_api_points_leaderboard_get","parameters":[{"name":"limit","in":"query","required":false,"schema":{"type":"integer","maximum":50,"minimum":1,"description":"æè¡æ¦æ°é","default":10,"title":"Limit"},"description":"æè¡æ¦æ°é"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/points/rules":{"get":{"tags":["ç§¯åç³»ç»"],"summary":"è·åç§¯åè§åè¯´æ","description":"è·åç§¯åè§åè¯´æ","operationId":"get_points_rules_api_points_rules_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}}}},"/api/help/requests":{"post":{"tags":["èµæºæ±å©"],"summary":"åå»ºèµæºæ±å©","description":"åå»ºèµæºæ±å©è¯·æ±","operationId":"create_help_request_api_help_requests_post","security":[{"HTTPBearer":[]}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/HelpRequestCreateRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}},"get":{"tags":["èµæºæ±å©"],"summary":"è·åèµæºæ±å©åè¡¨","description":"è·åèµæºæ±å©åè¡¨ï¼æ¯ææç¶æãèµæºç±»åç­éåå³é®è¯æç´¢","operationId":"get_help_requests_api_help_requests_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"page","in":"query","required":false,"schema":{"type":"integer","minimum":1,"description":"é¡µç ","default":1,"title":"Page"},"description":"é¡µç "},{"name":"size","in":"query","required":false,"schema":{"type":"integer","maximum":100,"minimum":1,"description":"æ¯é¡µæ°é","default":20,"title":"Size"},"description":"æ¯é¡µæ°é"},{"name":"status","in":"query","required":false,"schema":{"anyOf":[{"$ref":"#/components/schemas/HelpRequestStatus"},{"type":"null"}],"description":"ç¶æç­éï¼open(å¼æ¾ä¸­)ãresolved(å·²è§£å³)ãclosed(å·²å³é­)","title":"Status"},"description":"ç¶æç­éï¼open(å¼æ¾ä¸­)ãresolved(å·²è§£å³)ãclosed(å·²å³é­)"},{"name":"resource_type","in":"query","required":false,"schema":{"anyOf":[{"$ref":"#/components/schemas/ResourceType"},{"type":"null"}],"description":"èµæºç±»åç­éï¼movie(çµå½±)ãtv(çµè§å§)ãmusic(é³ä¹)ãsoftware(è½¯ä»¶)ãgame(æ¸¸æ)ãbook(ä¹¦ç±)ãdocument(ææ¡£)ãother(å¶ä»)","title":"Resource Type"},"description":"èµæºç±»åç­éï¼movie(çµå½±)ãtv(çµè§å§)ãmusic(é³ä¹)ãsoftware(è½¯ä»¶)ãgame(æ¸¸æ)ãbook(ä¹¦ç±)ãdocument(ææ¡£)ãother(å¶ä»)"},{"name":"search","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æç´¢å³é®è¯ï¼æ é¢åæè¿°ï¼","title":"Search"},"description":"æç´¢å³é®è¯ï¼æ é¢åæè¿°ï¼"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/help/requests/{request_id}":{"get":{"tags":["èµæºæ±å©"],"summary":"è·åèµæºæ±å©è¯¦æ","description":"è·åèµæºæ±å©è¯¦æ","operationId":"get_help_request_detail_api_help_requests__request_id__get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"integer","title":"Request Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/help/requests/{request_id}/answers":{"post":{"tags":["èµæºæ±å©"],"summary":"åç­èµæºæ±å©","description":"åç­èµæºæ±å©è¯·æ±","operationId":"create_help_answer_api_help_requests__request_id__answers_post","security":[{"HTTPBearer":[]}],"parameters":[{"name":"request_id","in":"path","required":true,"schema":{"type":"integer","title":"Request Id"}}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/HelpAnswerCreateRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/help/answers/{answer_id}/accept":{"post":{"tags":["èµæºæ±å©"],"summary":"éçº³èµæºæ±å©åç­","description":"éçº³èµæºæ±å©åç­","operationId":"accept_answer_api_help_answers__answer_id__accept_post","security":[{"HTTPBearer":[]}],"parameters":[{"name":"answer_id","in":"path","required":true,"schema":{"type":"integer","title":"Answer Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/help/answers/{answer_id}":{"delete":{"tags":["èµæºæ±å©"],"summary":"å é¤èµæºæ±å©åç­","description":"å é¤èµæºæ±å©åç­","operationId":"delete_answer_api_help_answers__answer_id__delete","security":[{"HTTPBearer":[]}],"parameters":[{"name":"answer_id","in":"path","required":true,"schema":{"type":"integer","title":"Answer Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/help/statistics":{"get":{"tags":["èµæºæ±å©"],"summary":"è·åèµæºæ±å©ç»è®¡ä¿¡æ¯","description":"è·åèµæºæ±å©ç»è®¡ä¿¡æ¯","operationId":"get_help_statistics_api_help_statistics_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}}}},"/api/titles/system":{"get":{"tags":["å¤´è¡ç³»ç»"],"summary":"è·åå¤´è¡ç³»ç»ä¿¡æ¯","description":"è·åå¤´è¡ç³»ç»ä¿¡æ¯","operationId":"get_title_system_api_titles_system_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}}}},"/api/titles/my":{"get":{"tags":["å¤´è¡ç³»ç»"],"summary":"è·åæçå¤´è¡ä¿¡æ¯","description":"è·åå½åç¨æ·çå¤´è¡ä¿¡æ¯","operationId":"get_my_title_info_api_titles_my_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/titles/leaderboard":{"get":{"tags":["å¤´è¡ç³»ç»"],"summary":"è·åå¤´è¡æè¡æ¦","description":"è·åå¤´è¡æè¡æ¦","operationId":"get_title_leaderboard_api_titles_leaderboard_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}}}},"/api/profile/me":{"get":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"è·åæçä¸ªäººä¿¡æ¯","description":"è·åå½åç¨æ·å®æ´ä¸ªäººä¿¡æ¯","operationId":"get_my_profile_api_profile_me_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}},"security":[{"HTTPBearer":[]}]},"put":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"æ´æ°æçä¸ªäººä¿¡æ¯","description":"æ¹éæ´æ°ç¨æ·åºç¡ä¿¡æ¯","operationId":"update_my_profile_api_profile_me_put","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/app__models__profile_models__UserProfileUpdateRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/profile/change-email":{"post":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"è¯·æ±æ´æ¹é®ç®±","description":"è¯·æ±æ´æ¹é®ç®±ï¼åééªè¯é®ä»¶ï¼","operationId":"request_email_change_api_profile_change_email_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChangeEmailRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/profile/verify-email-change":{"post":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"éªè¯é®ç®±æ´æ¹","description":"éªè¯é®ç®±æ´æ¹","operationId":"verify_email_change_api_profile_verify_email_change_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/VerifyEmailChangeRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/profile/upload-avatar":{"post":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"ä¸ä¼ å¤´å","description":"ä¸ä¼ ç¨æ·å¤´å\n\n- æ¯ææ ¼å¼ï¼JPG, PNG, GIF, WebP\n- èªå¨è½¬æ¢ä¸ºWebPæ ¼å¼ï¼è´¨é80%\n- èªå¨è°æ´ä¸º400x400å°ºå¯¸\n- å­å¨å°Cloudflare R2ï¼å¨çCDNå é\n- ä¸ä¼ æ°å¤´åæ¶ä¼èªå¨æ¸çæ§å¤´åæä»¶","operationId":"upload_avatar_api_profile_upload_avatar_post","requestBody":{"content":{"multipart/form-data":{"schema":{"$ref":"#/components/schemas/Body_upload_avatar_api_profile_upload_avatar_post"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/profile/change-password":{"post":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"ä¿®æ¹å¯ç ","description":"ä¿®æ¹å¯ç ","operationId":"change_password_api_profile_change_password_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/app__models__profile_models__ChangePasswordRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/profile/change-nickname":{"post":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"ä¿®æ¹æµç§°","description":"ä¿®æ¹æµç§°","operationId":"change_nickname_api_profile_change_nickname_post","requestBody":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ChangeNicknameRequest"}}},"required":true},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/profile/points-history":{"get":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"è·åæçç§¯ååå²","description":"è·åç¨æ·ç§¯åè·åæ¥å¿","operationId":"get_points_history_api_profile_points_history_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"page","in":"query","required":false,"schema":{"type":"integer","default":1,"title":"Page"}},{"name":"size","in":"query","required":false,"schema":{"type":"integer","default":20,"title":"Size"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/profile/help-requests":{"get":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"è·åæçæ±å©åè¡¨","description":"è·åæçæ±å©åè¡¨","operationId":"get_my_help_requests_api_profile_help_requests_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"page","in":"query","required":false,"schema":{"type":"integer","default":1,"title":"Page"}},{"name":"size","in":"query","required":false,"schema":{"type":"integer","default":20,"title":"Size"}},{"name":"status_filter","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Status Filter"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/profile/help-answers":{"get":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"è·åæçåç­åè¡¨","description":"è·åæçåç­åè¡¨","operationId":"get_my_help_answers_api_profile_help_answers_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"page","in":"query","required":false,"schema":{"type":"integer","default":1,"title":"Page"}},{"name":"size","in":"query","required":false,"schema":{"type":"integer","default":20,"title":"Size"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/profile/statistics":{"get":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"è·åæçç»è®¡ä¿¡æ¯","description":"è·åç¨æ·ç»è®¡ä¿¡æ¯","operationId":"get_profile_statistics_api_profile_statistics_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/profile/activity-summary":{"get":{"tags":["ä¸ªäººä¿¡æ¯ç®¡ç"],"summary":"è·åæçæ´»å¨æè¦","description":"è·åç¨æ·æ´»å¨æè¦","operationId":"get_activity_summary_api_profile_activity_summary_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ApiResponse"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/admin/config/tree":{"get":{"tags":["ç®¡çå-éç½®ç®¡ç"],"summary":"è·åéç½®æ å½¢ç»æ","description":"è·åå®æ´çéç½®æ å½¢ç»æï¼æ¯æå±çº§å±ç¤ºåæ³¨éæ¾ç¤º","operationId":"get_config_tree_api_admin_config_tree_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"show_sensitive","in":"query","required":false,"schema":{"type":"boolean","description":"æ¯å¦æ¾ç¤ºææä¿¡æ¯","default":false,"title":"Show Sensitive"},"description":"æ¯å¦æ¾ç¤ºææä¿¡æ¯"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConfigTreeResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/config/path/{path}":{"get":{"tags":["ç®¡çå-éç½®ç®¡ç"],"summary":"æè·¯å¾è·åéç½®","description":"æ ¹æ®éç½®è·¯å¾è·åç¹å®éç½®é¡¹çè¯¦ç»ä¿¡æ¯","operationId":"get_config_by_path_api_admin_config_path__path__get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"path","in":"path","required":true,"schema":{"type":"string","title":"Path"}},{"name":"show_sensitive","in":"query","required":false,"schema":{"type":"boolean","description":"æ¯å¦æ¾ç¤ºææä¿¡æ¯","default":false,"title":"Show Sensitive"},"description":"æ¯å¦æ¾ç¤ºææä¿¡æ¯"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConfigPathResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}},"put":{"tags":["ç®¡çå-éç½®ç®¡ç"],"summary":"æè·¯å¾ä¿®æ¹éç½®","description":"æ ¹æ®éç½®è·¯å¾ç²¾ç¡®ä¿®æ¹ç¹å®éç½®é¡¹çå¼","operationId":"update_config_by_path_api_admin_config_path__path__put","security":[{"HTTPBearer":[]}],"parameters":[{"name":"path","in":"path","required":true,"schema":{"type":"string","title":"Path"}}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConfigPathRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/config/validate":{"post":{"tags":["ç®¡çå-éç½®ç®¡ç"],"summary":"éªè¯éç½®å¼","description":"éªè¯éç½®å¼æ¯å¦ç¬¦åè¦æ±","operationId":"validate_config_value_api_admin_config_validate_post","security":[{"HTTPBearer":[]}],"parameters":[{"name":"path","in":"query","required":true,"schema":{"type":"string","description":"éç½®è·¯å¾","title":"Path"},"description":"éç½®è·¯å¾"},{"name":"value","in":"query","required":true,"schema":{"description":"éç½®å¼","title":"Value"},"description":"éç½®å¼"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ValidationResult"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/config/schema":{"get":{"tags":["ç®¡çå-éç½®ç®¡ç"],"summary":"è·åéç½®æ¨¡å¼","description":"è·åéç½®é¡¹çæ¨¡å¼å®ä¹åéªè¯è§å","operationId":"get_config_schema_api_admin_config_schema_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ConfigSchemaResponse"}}}}},"security":[{"HTTPBearer":[]}]}},"/api/admin/config/search":{"get":{"tags":["ç®¡çå-éç½®ç®¡ç"],"summary":"æç´¢éç½®é¡¹","description":"æ ¹æ®å³é®è¯æç´¢éç½®é¡¹","operationId":"search_config_api_admin_config_search_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"query","in":"query","required":true,"schema":{"type":"string","description":"æç´¢å³é®è¯","title":"Query"},"description":"æç´¢å³é®è¯"},{"name":"show_sensitive","in":"query","required":false,"schema":{"type":"boolean","description":"æ¯å¦æ¾ç¤ºææä¿¡æ¯","default":false,"title":"Show Sensitive"},"description":"æ¯å¦æ¾ç¤ºææä¿¡æ¯"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/feedback/stats":{"get":{"tags":["ç®¡çå-èµæºåé¦ç®¡ç"],"summary":"ç®¡çååé¦ç»è®¡","description":"è·ååé¦çç»è®¡ä¿¡æ¯","operationId":"get_admin_feedback_stats_api_admin_feedback_stats_get","responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}}},"security":[{"HTTPBearer":[]}]}},"/api/admin/feedback":{"get":{"tags":["ç®¡çå-èµæºåé¦ç®¡ç"],"summary":"ç®¡çååé¦åè¡¨","description":"è·ååé¦åè¡¨ï¼æ¯æé«çº§ç­éåæç´¢","operationId":"get_admin_feedback_api_admin_feedback_get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"page","in":"query","required":false,"schema":{"type":"integer","minimum":1,"description":"é¡µç ","default":1,"title":"Page"},"description":"é¡µç "},{"name":"size","in":"query","required":false,"schema":{"type":"integer","maximum":100,"minimum":1,"description":"æ¯é¡µæ°é","default":20,"title":"Size"},"description":"æ¯é¡µæ°é"},{"name":"invalid_type","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"å¤±æç±»åç­é: 1=é¾æ¥éè¯¯, 2=èµæºå¤±æ, 3=æä»¶ä¸å­å¨","title":"Invalid Type"},"description":"å¤±æç±»åç­é: 1=é¾æ¥éè¯¯, 2=èµæºå¤±æ, 3=æä»¶ä¸å­å¨"},{"name":"pan_type","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"ç½çç±»åç­é: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç","title":"Pan Type"},"description":"ç½çç±»åç­é: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç"},{"name":"is_verified","in":"query","required":false,"schema":{"anyOf":[{"type":"boolean"},{"type":"null"}],"description":"æ¯å¦å·²éªè¯ç­é","title":"Is Verified"},"description":"æ¯å¦å·²éªè¯ç­é"},{"name":"keyword","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æç´¢èµæºIDææ é¢","title":"Keyword"},"description":"æç´¢èµæºIDææ é¢"},{"name":"start_date","in":"query","required":false,"schema":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"description":"å¼å§æ¥æ","title":"Start Date"},"description":"å¼å§æ¥æ"},{"name":"end_date","in":"query","required":false,"schema":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"description":"ç»ææ¥æ","title":"End Date"},"description":"ç»ææ¥æ"}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/admin/feedback/{feedback_id}":{"get":{"tags":["ç®¡çå-èµæºåé¦ç®¡ç"],"summary":"ç®¡çååé¦è¯¦æ","description":"è·ååä¸ªåé¦çè¯¦ç»ä¿¡æ¯ï¼åå«å³èèµæºåç¸å³åé¦","operationId":"get_admin_feedback_detail_api_admin_feedback__feedback_id__get","security":[{"HTTPBearer":[]}],"parameters":[{"name":"feedback_id","in":"path","required":true,"schema":{"type":"integer","title":"Feedback Id"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/search":{"get":{"tags":["search"],"summary":"æç´¢ç½çèµæº","description":"æ ¹æ®ä¼ å¥çpan_typeï¼ä»å¤ä¸ªç¬è«æå¡è·åèµæºåè¡¨","operationId":"search_api_search_get","parameters":[{"name":"keyword","in":"query","required":true,"schema":{"type":"string","description":"æç´¢å³é®è¯","title":"Keyword"},"description":"æç´¢å³é®è¯"},{"name":"page","in":"query","required":false,"schema":{"type":"integer","description":"é¡µç ","default":1,"title":"Page"},"description":"é¡µç "},{"name":"limit","in":"query","required":false,"schema":{"type":"integer","description":"æ¯é¡µç»ææ°","default":10,"title":"Limit"},"description":"æ¯é¡µç»ææ°"},{"name":"file_type","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æä»¶ç±»åè¿æ»¤: video(è§é¢), audio(é³é¢), image(å¾ç), document(ææ¡£), archive(åç¼©), application(åºç¨)","title":"File Type"},"description":"æä»¶ç±»åè¿æ»¤: video(è§é¢), audio(é³é¢), image(å¾ç), document(ææ¡£), archive(åç¼©), application(åºç¨)"},{"name":"pan_type","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"ç½çç±»å: 0=å¨é¨, 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3-é¿éäºç, 4-è¿é·ç½ç","title":"Pan Type"},"description":"ç½çç±»å: 0=å¨é¨, 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3-é¿éäºç, 4-è¿é·ç½ç"},{"name":"sort_by","in":"query","required":false,"schema":{"type":"string","description":"æåºå­æ®µ: relevance, file_size, updated_at","default":"relevance","title":"Sort By"},"description":"æåºå­æ®µ: relevance, file_size, updated_at"},{"name":"sort_order","in":"query","required":false,"schema":{"type":"string","description":"æåºæ¹å: asc, desc","default":"desc","title":"Sort Order"},"description":"æåºæ¹å: asc, desc"},{"name":"exact","in":"query","required":false,"schema":{"type":"boolean","description":"æ¯å¦ç²¾åæç´¢","default":false,"title":"Exact"},"description":"æ¯å¦ç²¾åæç´¢"},{"name":"time_filter","in":"query","required":false,"schema":{"allOf":[{"$ref":"#/components/schemas/TimeFilter"}],"description":"æ¶é´è¿æ»¤: all(å¨é¨æ¶é´), week(æè¿ä¸å¨), half_month(æè¿åæ), month(æè¿1æ), half_year(æè¿åå¹´), year(æè¿ä¸å¹´)","default":"all","title":"Time Filter"},"description":"æ¶é´è¿æ»¤: all(å¨é¨æ¶é´), week(æè¿ä¸å¨), half_month(æè¿åæ), month(æè¿1æ), half_year(æè¿åå¹´), year(æè¿ä¸å¹´)"},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SearchResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/get_share":{"post":{"tags":["resource"],"summary":"è·åç½çåäº«é¾æ¥","description":"æ ¹æ®resource_idè·åç½çåäº«é¾æ¥","operationId":"get_share_link_api_get_share_post","parameters":[{"name":"platform","in":"query","required":true,"schema":{"type":"string","description":"ç½çå¹³å°ç±»å","title":"Platform"},"description":"ç½çå¹³å°ç±»å"},{"name":"resource_id","in":"query","required":true,"schema":{"type":"string","description":"èµæºID","title":"Resource Id"},"description":"èµæºID"},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ShareLinkResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/cached_resources":{"get":{"tags":["resource"],"summary":"è·åç¼å­èµæº","description":"æ ¹æ®èµæºåç§°/æä»¶å(æ¯ææ¨¡ç³æç´¢)ãç½çç±»åãæ¯å¦åªè¿åéªè¯ææçèµæºãè¿åç»ææ°ééå¶ãé¡µç ãæåºå­æ®µãæåºæ¹åãæ¯å¦å¯ç¨ç¸å³æç´¢(åè¯å¹é)è·åç¼å­èµæº","operationId":"get_cached_resources_api_cached_resources_get","parameters":[{"name":"title","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"èµæºåç§°/æä»¶å(æ¯ææ¨¡ç³æç´¢)","title":"Title"},"description":"èµæºåç§°/æä»¶å(æ¯ææ¨¡ç³æç´¢)"},{"name":"pan_type","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"ç½çç±»å: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç","title":"Pan Type"},"description":"ç½çç±»å: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç"},{"name":"file_type","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æä»¶ç±»åè¿æ»¤: video(è§é¢), audio(é³é¢), image(å¾ç), document(ææ¡£), archive(åç¼©), application(åºç¨)","title":"File Type"},"description":"æä»¶ç±»åè¿æ»¤: video(è§é¢), audio(é³é¢), image(å¾ç), document(ææ¡£), archive(åç¼©), application(åºç¨)"},{"name":"valid_only","in":"query","required":false,"schema":{"type":"boolean","description":"åªè¿åéªè¯ææçèµæº","default":false,"title":"Valid Only"},"description":"åªè¿åéªè¯ææçèµæº"},{"name":"limit","in":"query","required":false,"schema":{"type":"integer","description":"è¿åç»ææ°ééå¶","default":30,"title":"Limit"},"description":"è¿åç»ææ°ééå¶"},{"name":"page","in":"query","required":false,"schema":{"type":"integer","description":"é¡µç ï¼ä»1å¼å§","default":1,"title":"Page"},"description":"é¡µç ï¼ä»1å¼å§"},{"name":"sort_by","in":"query","required":false,"schema":{"type":"string","description":"æåºå­æ®µ: relevance, access_count, created_at, title","default":"relevance","title":"Sort By"},"description":"æåºå­æ®µ: relevance, access_count, created_at, title"},{"name":"sort_order","in":"query","required":false,"schema":{"type":"string","description":"æåºæ¹å: asc, desc","default":"desc","title":"Sort Order"},"description":"æåºæ¹å: asc, desc"},{"name":"related_search","in":"query","required":false,"schema":{"type":"boolean","description":"å¯ç¨ç¸å³æç´¢(åè¯å¹é)","default":true,"title":"Related Search"},"description":"å¯ç¨ç¸å³æç´¢(åè¯å¹é)"},{"name":"exact","in":"query","required":false,"schema":{"type":"boolean","description":"æ¯å¦ç²¾åæç´¢","default":false,"title":"Exact"},"description":"æ¯å¦ç²¾åæç´¢"},{"name":"user","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æ ¹æ®åäº«ç¨æ·åæ¥æ¾èµæºãä¼ ç©ºå­ç¬¦ä¸²å¯æ¥æ¾å¿ååäº«çèµæºã","title":"User"},"description":"æ ¹æ®åäº«ç¨æ·åæ¥æ¾èµæºãä¼ ç©ºå­ç¬¦ä¸²å¯æ¥æ¾å¿ååäº«çèµæºã"},{"name":"time_filter","in":"query","required":false,"schema":{"allOf":[{"$ref":"#/components/schemas/TimeFilter"}],"description":"æ¶é´è¿æ»¤: all(å¨é¨æ¶é´), week(æè¿ä¸å¨), half_month(æè¿åæ), month(æè¿1æ), half_year(æè¿åå¹´), year(æè¿ä¸å¹´)","default":"all","title":"Time Filter"},"description":"æ¶é´è¿æ»¤: all(å¨é¨æ¶é´), week(æè¿ä¸å¨), half_month(æè¿åæ), month(æè¿1æ), half_year(æè¿åå¹´), year(æè¿ä¸å¹´)"},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/submit_resources":{"post":{"tags":["resource"],"summary":"æ¹éæäº¤èµæºé¾æ¥è¿è¡å¤ç (å¼æ­¥)","description":"æ¥æ¶ä¸æ¹ç½çURLï¼ç«å³è¿åä»»å¡IDï¼å¹¶å¨åå°å¼æ­¥å¤çãå¤çéåº¦å¿«ï¼ä¸ä¼é»å¡æå¡ã","operationId":"submit_resources_for_processing_api_submit_resources_post","security":[{"HTTPBearer":[]}],"parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceSubmissionRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BatchSubmissionResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/query_submission_status":{"post":{"tags":["resource"],"summary":"éè¿URLåè¡¨æ¥è¯¢èµæºæäº¤ä»»å¡çç¶æ","description":"æ¥æ¶ä¸ä¸ªåå«åå§åäº«URLçåè¡¨ï¼è¿åæ¯ä¸ªURLå³èçæææäº¤ä»»å¡çå½åç¶æãæ³¨æï¼ç±äºåå°ä»»å¡æ¯å¼æ­¥çï¼ç¶ææ´æ°å¯è½å­å¨å»¶è¿ã","operationId":"query_submission_status_by_urls_api_query_submission_status_post","parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/QuerySubmissionStatusRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/IndividualTaskStatus"},"title":"Response Query Submission Status By Urls Api Query Submission Status Post"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/resource/{resource_key}":{"get":{"tags":["resource"],"summary":"è·ååä¸ªèµæºè¯¦æ(SEOä¼å)","description":"æ ¹æ®resource_keyè·ååä¸ªèµæºçè¯¦ç»ä¿¡æ¯ï¼å¹¶å¨æçæSEOåæ°æ®ã","operationId":"get_resource_details_api_resource__resource_key__get","parameters":[{"name":"resource_key","in":"path","required":true,"schema":{"type":"string","title":"Resource Key"}},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceDetailResponse"}}}},"404":{"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceDetailErrorResponse"}}},"description":"Not Found"},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/resources/sitemap":{"get":{"tags":["resource"],"summary":"è·åç«ç¹å°å¾èµæº","description":"ä¸ºç«ç¹å°å¾çæä¼åçãè½»éçº§çèµæºåè¡¨ã","operationId":"get_sitemap_resources_api_resources_sitemap_get","parameters":[{"name":"limit","in":"query","required":false,"schema":{"type":"integer","maximum":5000,"exclusiveMinimum":0,"description":"è¿åç»ææ°ééå¶","default":1000,"title":"Limit"},"description":"è¿åç»ææ°ééå¶"},{"name":"page","in":"query","required":false,"schema":{"type":"integer","exclusiveMinimum":0,"description":"é¡µç ï¼ä»1å¼å§","default":1,"title":"Page"},"description":"é¡µç ï¼ä»1å¼å§"},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/resources/count":{"get":{"tags":["resource"],"summary":"è·åèµæºæ»æ°","description":"è·åç³»ç»ä¸­ææèµæºçæ»æ°ã","operationId":"get_resources_count_api_resources_count_get","parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/sitemap-index":{"get":{"tags":["resource"],"summary":"è·åsitemapç´¢å¼ä¿¡æ¯","description":"çæsitemapç´¢å¼ï¼æ¯ä¸ªåççlastmodåºäºè¯¥åçä¸­ææ°èµæºçæ´æ°æ¶é´ã","operationId":"get_sitemap_index_api_sitemap_index_get","parameters":[{"name":"limit_per_sitemap","in":"query","required":false,"schema":{"type":"integer","maximum":5000,"exclusiveMinimum":0,"description":"æ¯ä¸ªsitemapæä»¶çèµæºæ°ééå¶","default":1000,"title":"Limit Per Sitemap"},"description":"æ¯ä¸ªsitemapæä»¶çèµæºæ°ééå¶"},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/custom_search":{"get":{"tags":["resource"],"summary":"å®å¶åæç´¢(Meilisearch)","description":"ä½¿ç¨Meilisearchè¿è¡æç´¢ï¼æå¤è¿å10æ¡æ°æ®ï¼titleä¸è¿è¡åè¯å¤çãåæ°åæ¬title, file_type, exact, sort_by, sort_order, valid_only, user, time_filterã","operationId":"custom_search_api_custom_search_get","parameters":[{"name":"title","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"èµæºåç§°/æä»¶å(æ¯ææ¨¡ç³æç´¢)","title":"Title"},"description":"èµæºåç§°/æä»¶å(æ¯ææ¨¡ç³æç´¢)"},{"name":"pan_type","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"ç½çç±»å: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç","title":"Pan Type"},"description":"ç½çç±»å: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç"},{"name":"file_type","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æä»¶ç±»åè¿æ»¤","title":"File Type"},"description":"æä»¶ç±»åè¿æ»¤"},{"name":"exact","in":"query","required":false,"schema":{"type":"boolean","description":"æ¯å¦ç²¾åæç´¢","default":false,"title":"Exact"},"description":"æ¯å¦ç²¾åæç´¢"},{"name":"sort_by","in":"query","required":false,"schema":{"type":"string","description":"æåºå­æ®µ: updated_at, created_at, access_count, title","default":"updated_at","title":"Sort By"},"description":"æåºå­æ®µ: updated_at, created_at, access_count, title"},{"name":"sort_order","in":"query","required":false,"schema":{"type":"string","description":"æåºæ¹å: asc, desc","default":"desc","title":"Sort Order"},"description":"æåºæ¹å: asc, desc"},{"name":"valid_only","in":"query","required":false,"schema":{"type":"boolean","description":"åªè¿åéªè¯ææçèµæº","default":false,"title":"Valid Only"},"description":"åªè¿åéªè¯ææçèµæº"},{"name":"user","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"æ ¹æ®åäº«ç¨æ·åæ¥æ¾èµæºãä¼ ç©ºå­ç¬¦ä¸²å¯æ¥æ¾å¿ååäº«çèµæºã","title":"User"},"description":"æ ¹æ®åäº«ç¨æ·åæ¥æ¾èµæºãä¼ ç©ºå­ç¬¦ä¸²å¯æ¥æ¾å¿ååäº«çèµæºã"},{"name":"limit","in":"query","required":false,"schema":{"type":"integer","maximum":10,"description":"è¿åæ¡æ°","default":10,"title":"Limit"},"description":"è¿åæ¡æ°"},{"name":"query_type","in":"query","required":false,"schema":{"type":"string","description":"æ¥è¯¢ç±»å: æè¿æ´æ°,ç¸å³æ¨è","default":"æè¿æ´æ°","title":"Query Type"},"description":"æ¥è¯¢ç±»å: æè¿æ´æ°,ç¸å³æ¨è"},{"name":"time_filter","in":"query","required":false,"schema":{"allOf":[{"$ref":"#/components/schemas/TimeFilter"}],"description":"æ¶é´è¿æ»¤: all(å¨é¨æ¶é´), week(æè¿ä¸å¨), half_month(æè¿åæ), month(æè¿1æ), half_year(æè¿åå¹´), year(æè¿ä¸å¹´)","default":"all","title":"Time Filter"},"description":"æ¶é´è¿æ»¤: all(å¨é¨æ¶é´), week(æè¿ä¸å¨), half_month(æè¿åæ), month(æè¿1æ), half_year(æè¿åå¹´), year(æè¿ä¸å¹´)"},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/":{"get":{"tags":["stats"],"summary":"APIè·¯ç±","description":"APIè·¯ç±","operationId":"root_api__get","parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/resource_stats":{"get":{"tags":["stats"],"summary":"è·åèµæºç»è®¡ä¿¡æ¯","description":"è·åèµæºç»è®¡ä¿¡æ¯ï¼åæ¬æ»èµæºæ°ãæ¨æ¥æ°å¢åå¾å¤çä»»å¡æ°é","operationId":"get_resource_stats_api_resource_stats_get","parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/check_service":{"get":{"tags":["stats"],"summary":"æ£æ¥ç½çæå¡ç¶æ","description":"æ£æ¥ç½çæå¡ç¶æ","operationId":"check_service_api_check_service_get","parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/db_status":{"get":{"tags":["stats"],"summary":"æ£æ¥æ°æ®åºè¿æ¥ç¶æ","description":"æ£æ¥æ°æ®åºè¿æ¥ç¶æ","operationId":"db_status_api_db_status_get","parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/check_resource_status":{"get":{"tags":["stats"],"summary":"æ£æ¥ç½çèµæºç¶æ","description":"æ£æ¥ç½çèµæºç¶ææ¯å¦ææ","operationId":"check_resource_status_api_check_resource_status_get","parameters":[{"name":"share_url","in":"query","required":false,"schema":{"type":"string","description":"ç½çåäº«é¾æ¥","title":"Share Url"},"description":"ç½çåäº«é¾æ¥"},{"name":"pan_type","in":"query","required":false,"schema":{"anyOf":[{"type":"integer"},{"type":"null"}],"description":"ç½çç±»å: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç","title":"Pan Type"},"description":"ç½çç±»å: 1=ç¾åº¦ç½ç, 2=å¤¸åç½ç, 3=é¿éäºç, 4=è¿é·ç½ç"},{"name":"resource_id","in":"query","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"description":"èµæºID","title":"Resource Id"},"description":"èµæºID"},{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}},"/api/report_invalid_resource":{"post":{"tags":["feedback"],"summary":"æ¥åèµæºå¤±æ","description":"æ¥åèµæºå¤±æï¼å¹¶è¿è¡å®æ¶éªè¯ï¼å¦æèµæºç¡®å®å¤±æåå é¤è¯¥èµæº","operationId":"report_invalid_resource_api_report_invalid_resource_post","parameters":[{"name":"referer","in":"header","required":false,"schema":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Referer"}}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceInvalidFeedbackRequest"}}}},"responses":{"200":{"description":"Successful Response","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ResourceInvalidFeedbackResponse"}}}},"422":{"description":"Validation Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HTTPValidationError"}}}}}}}},"components":{"schemas":{"ApiResponse":{"properties":{"status":{"type":"string","title":"Status","description":"ååºç¶æ","default":"success"},"message":{"type":"string","title":"Message","description":"ååºæ¶æ¯","default":""},"data":{"anyOf":[{"type":"object"},{"type":"null"}],"title":"Data","description":"ååºæ°æ®"}},"type":"object","title":"ApiResponse","description":"éç¨APIååºæ¨¡å"},"BatchSubmissionResponse":{"properties":{"batch_id":{"type":"string","format":"uuid","title":"Batch Id"},"message":{"type":"string","title":"Message"},"total_submitted":{"type":"integer","title":"Total Submitted"},"accepted_for_processing":{"type":"integer","title":"Accepted For Processing"},"initial_results":{"items":{"$ref":"#/components/schemas/SubmittedResourceInfo"},"type":"array","title":"Initial Results"}},"type":"object","required":["batch_id","message","total_submitted","accepted_for_processing","initial_results"],"title":"BatchSubmissionResponse","description":"èµæºæ¹éæäº¤åçååºæ¨¡å"},"Body_upload_avatar_api_profile_upload_avatar_post":{"properties":{"file":{"type":"string","format":"binary","title":"File"}},"type":"object","required":["file"],"title":"Body_upload_avatar_api_profile_upload_avatar_post"},"ChangeEmailRequest":{"properties":{"new_email":{"type":"string","format":"email","title":"New Email","description":"æ°é®ç®±å°å"},"password":{"type":"string","title":"Password","description":"å½åå¯ç "}},"type":"object","required":["new_email","password"],"title":"ChangeEmailRequest","description":"æ´æ¹é®ç®±è¯·æ±æ¨¡å"},"ChangeNicknameRequest":{"properties":{"nickname":{"type":"string","maxLength":100,"minLength":2,"title":"Nickname","description":"æ°æµç§°"},"reason":{"anyOf":[{"type":"string","maxLength":255},{"type":"null"}],"title":"Reason","description":"ä¿®æ¹åå "}},"type":"object","required":["nickname"],"title":"ChangeNicknameRequest","description":"ä¿®æ¹æµç§°è¯·æ±æ¨¡å"},"CloudDiskType":{"type":"string","enum":["baidu","aliyun","quark","xunlei","all"],"title":"CloudDiskType","description":"ç½çç±»åæä¸¾"},"ConfigPathRequest":{"properties":{"path":{"type":"string","title":"Path","description":"éç½®è·¯å¾","example":"baidu_accounts.0.cookie"},"value":{"title":"Value","description":"æ°éç½®å¼"},"comment":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Comment","description":"ä¿®æ¹è¯´æ"}},"type":"object","required":["path","value"],"title":"ConfigPathRequest","description":"éç½®è·¯å¾è¯·æ±"},"ConfigPathResponse":{"properties":{"path":{"type":"string","title":"Path","description":"éç½®è·¯å¾"},"value":{"title":"Value","description":"éç½®å¼"},"type":{"type":"string","title":"Type","description":"æ°æ®ç±»å"},"comment":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Comment","description":"æ³¨é"},"sensitive":{"type":"boolean","title":"Sensitive","description":"æ¯å¦ææä¿¡æ¯","default":false},"parent_path":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Parent Path","description":"ç¶çº§è·¯å¾"},"children_paths":{"anyOf":[{"items":{"type":"string"},"type":"array"},{"type":"null"}],"title":"Children Paths","description":"å­çº§è·¯å¾åè¡¨"}},"type":"object","required":["path","value","type"],"title":"ConfigPathResponse","description":"éç½®è·¯å¾ååº"},"ConfigSchema":{"properties":{"name":{"type":"string","title":"Name","description":"æ¨¡å¼åç§°"},"description":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Description","description":"æ¨¡å¼æè¿°"},"fields":{"items":{"$ref":"#/components/schemas/ConfigSchemaField"},"type":"array","title":"Fields","description":"å­æ®µåè¡¨"},"required_fields":{"items":{"type":"string"},"type":"array","title":"Required Fields","description":"å¿å¡«å­æ®µåè¡¨","default":[]}},"type":"object","required":["name","fields"],"title":"ConfigSchema","description":"éç½®æ¨¡å¼"},"ConfigSchemaField":{"properties":{"name":{"type":"string","title":"Name","description":"å­æ®µå"},"type":{"type":"string","title":"Type","description":"æ°æ®ç±»å"},"required":{"type":"boolean","title":"Required","description":"æ¯å¦å¿å¡«","default":false},"default":{"title":"Default","description":"é»è®¤å¼"},"description":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Description","description":"å­æ®µæè¿°"},"validation_rules":{"anyOf":[{"type":"object"},{"type":"null"}],"title":"Validation Rules","description":"éªè¯è§å"},"sensitive":{"type":"boolean","title":"Sensitive","description":"æ¯å¦ææä¿¡æ¯","default":false},"enum_values":{"anyOf":[{"items":{},"type":"array"},{"type":"null"}],"title":"Enum Values","description":"æä¸¾å¼"}},"type":"object","required":["name","type"],"title":"ConfigSchemaField","description":"éç½®æ¨¡å¼å­æ®µ"},"ConfigSchemaResponse":{"properties":{"schemas":{"additionalProperties":{"$ref":"#/components/schemas/ConfigSchema"},"type":"object","title":"Schemas","description":"éç½®æ¨¡å¼å­å¸"},"total_schemas":{"type":"integer","title":"Total Schemas","description":"æ»æ¨¡å¼æ°"}},"type":"object","required":["schemas","total_schemas"],"title":"ConfigSchemaResponse","description":"éç½®æ¨¡å¼ååº"},"ConfigTreeNode":{"properties":{"key":{"type":"string","title":"Key","description":"éç½®é®"},"display_name":{"type":"string","title":"Display Name","description":"æ¾ç¤ºåç§°"},"path":{"type":"string","title":"Path","description":"å®æ´è·¯å¾"},"type":{"type":"string","title":"Type","description":"æ°æ®ç±»å"},"value":{"title":"Value","description":"éç½®å¼"},"comment":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Comment","description":"æ³¨é"},"children":{"anyOf":[{"additionalProperties":{"$ref":"#/components/schemas/ConfigTreeNode"},"type":"object"},{"type":"null"}],"title":"Children","description":"å­èç¹"},"is_leaf":{"type":"boolean","title":"Is Leaf","description":"æ¯å¦ä¸ºå¶å­èç¹","default":true},"sensitive":{"type":"boolean","title":"Sensitive","description":"æ¯å¦ææä¿¡æ¯","default":false},"required":{"type":"boolean","title":"Required","description":"æ¯å¦å¿å¡«","default":false},"validation_rules":{"anyOf":[{"type":"object"},{"type":"null"}],"title":"Validation Rules","description":"éªè¯è§å"},"effect_type":{"type":"string","title":"Effect Type","description":"çææ¹å¼","default":"immediate"}},"type":"object","required":["key","display_name","path","type","value"],"title":"ConfigTreeNode","description":"éç½®æ èç¹"},"ConfigTreeResponse":{"properties":{"tree":{"additionalProperties":{"$ref":"#/components/schemas/ConfigTreeNode"},"type":"object","title":"Tree","description":"éç½®æ "},"total_nodes":{"type":"integer","title":"Total Nodes","description":"æ»èç¹æ°"},"max_depth":{"type":"integer","title":"Max Depth","description":"æå¤§æ·±åº¦"}},"type":"object","required":["tree","total_nodes","max_depth"],"title":"ConfigTreeResponse","description":"éç½®æ ååº"},"EmailVerificationRequest":{"properties":{"token":{"type":"string","title":"Token","description":"éªè¯ä»¤ç"}},"type":"object","required":["token"],"title":"EmailVerificationRequest","description":"é®ç®±éªè¯è¯·æ±æ¨¡å"},"ForgotPasswordRequest":{"properties":{"email":{"type":"string","format":"email","title":"Email","description":"é®ç®±å°å"}},"type":"object","required":["email"],"title":"ForgotPasswordRequest","description":"å¿è®°å¯ç è¯·æ±æ¨¡å"},"HTTPValidationError":{"properties":{"detail":{"items":{"$ref":"#/components/schemas/ValidationError"},"type":"array","title":"Detail"}},"type":"object","title":"HTTPValidationError"},"HelpAnswerCreateRequest":{"properties":{"resource_title":{"type":"string","maxLength":200,"minLength":1,"title":"Resource Title","description":"èµæºæ é¢"},"resource_link":{"type":"string","maxLength":500,"minLength":1,"title":"Resource Link","description":"èµæºé¾æ¥"},"cloud_disk_type":{"allOf":[{"$ref":"#/components/schemas/CloudDiskType"}],"description":"ç½çç±»å"},"additional_info":{"anyOf":[{"type":"string","maxLength":1000},{"type":"null"}],"title":"Additional Info","description":"è¡¥åè¯´æ"},"should_archive":{"type":"boolean","title":"Should Archive","description":"æ¯å¦å¥åº","default":false}},"type":"object","required":["resource_title","resource_link","cloud_disk_type"],"title":"HelpAnswerCreateRequest","description":"åå»ºåç­è¯·æ±æ¨¡å"},"HelpRequestCreateRequest":{"properties":{"title":{"type":"string","maxLength":200,"minLength":1,"title":"Title","description":"èµæºåç§°"},"description":{"anyOf":[{"type":"string","maxLength":2000},{"type":"null"}],"title":"Description","description":"è¯¦ç»æè¿°"},"cloud_disk_types":{"items":{"$ref":"#/components/schemas/CloudDiskType"},"type":"array","minItems":1,"title":"Cloud Disk Types","description":"ç½çç±»ååè¡¨"},"resource_type":{"allOf":[{"$ref":"#/components/schemas/ResourceType"}],"description":"èµæºç±»å","default":"other"}},"type":"object","required":["title","cloud_disk_types"],"title":"HelpRequestCreateRequest","description":"åå»ºæ±å©è¯·æ±æ¨¡å"},"HelpRequestStatus":{"type":"string","enum":["open","resolved","closed"],"title":"HelpRequestStatus","description":"æ±å©ç¶ææä¸¾"},"IndividualTaskStatus":{"properties":{"task_id":{"type":"string","format":"uuid","title":"Task Id"},"original_url":{"type":"string","title":"Original Url"},"status":{"$ref":"#/components/schemas/TaskStatus"},"resource_title":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Resource Title"},"pan_resource_id":{"anyOf":[{"type":"integer"},{"type":"null"}],"title":"Pan Resource Id"},"error_message":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Error Message"},"updated_at":{"type":"string","format":"date-time","title":"Updated At"},"batch_id":{"anyOf":[{"type":"string","format":"uuid"},{"type":"null"}],"title":"Batch Id"}},"type":"object","required":["task_id","original_url","status","updated_at"],"title":"IndividualTaskStatus","description":"åä¸ªä»»å¡çè¯¦ç»ç¶æ"},"PointsAdjustRequest":{"properties":{"user_id":{"type":"integer","title":"User Id","description":"ç¨æ·ID"},"amount":{"type":"integer","title":"Amount","description":"è°æ´æ°éï¼æ­£æ°ä¸ºå¢å ï¼è´æ°ä¸ºåå°ï¼"},"description":{"type":"string","title":"Description","description":"è°æ´åå "}},"type":"object","required":["user_id","amount","description"],"title":"PointsAdjustRequest","description":"ç§¯åè°æ´è¯·æ±æ¨¡å"},"QuerySubmissionStatusRequest":{"properties":{"urls":{"items":{"type":"string","maxLength":2048,"minLength":10},"type":"array","maxItems":100,"minItems":1,"title":"Urls","description":"è¦æ¥è¯¢ç¶æçåå§URLåè¡¨"}},"type":"object","required":["urls"],"title":"QuerySubmissionStatusRequest","description":"æURLæ¥è¯¢æäº¤ç¶æçè¯·æ±æ¨¡å"},"ResetPasswordRequest":{"properties":{"token":{"type":"string","title":"Token","description":"éç½®ä»¤ç"},"new_password":{"type":"string","maxLength":128,"minLength":8,"title":"New Password","description":"æ°å¯ç "}},"type":"object","required":["token","new_password"],"title":"ResetPasswordRequest","description":"éç½®å¯ç è¯·æ±æ¨¡å"},"ResourceDetailErrorResponse":{"properties":{"status":{"type":"string","title":"Status"},"message":{"type":"string","title":"Message"}},"type":"object","required":["status","message"],"title":"ResourceDetailErrorResponse","description":"è·åèµæºè¯¦æå¤±è´¥æ¶çéè¯¯ååºæ¨¡åã"},"ResourceDetailResponse":{"properties":{"id":{"type":"integer","title":"Id"},"resource_key":{"type":"string","title":"Resource Key"},"pan_type":{"type":"integer","title":"Pan Type"},"original_url":{"type":"string","title":"Original Url"},"title":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Title"},"is_parsed":{"type":"boolean","title":"Is Parsed"},"author":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Author"},"author_avatar":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Author Avatar"},"is_mine":{"type":"boolean","title":"Is Mine"},"verified_status":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Verified Status"},"share_url":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Share Url"},"share_pwd":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Share Pwd"},"created_at":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"title":"Created At"},"updated_at":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"title":"Updated At"},"expiry_date":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"title":"Expiry Date"},"file_type":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"File Type"},"file_size":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"File Size"},"access_count":{"type":"integer","title":"Access Count"},"text_content":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Text Content"},"seo_title":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Seo Title","description":"ä¸ºSEOçæçé¡µé¢æ é¢"},"seo_description":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Seo Description","description":"ä¸ºSEOçæçé¡µé¢æè¿°"},"seo_keywords":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Seo Keywords","description":"ä¸ºSEOçæçå³é®è¯"}},"type":"object","required":["id","resource_key","pan_type","original_url","title","is_parsed","author","author_avatar","is_mine","verified_status","share_url","share_pwd","created_at","updated_at","expiry_date","file_type","file_size","access_count","text_content"],"title":"ResourceDetailResponse","description":"è·ååä¸ªèµæºè¯¦æçAPIååºæ¨¡åã\nåå«äºèµæºçææä¸»è¦å­æ®µä»¥åä¸ºSEOçæçåæ°æ®ã"},"ResourceInvalidFeedbackRequest":{"properties":{"resource_id":{"type":"string","title":"Resource Id"},"pan_type":{"type":"integer","title":"Pan Type"},"invalid_type":{"type":"integer","title":"Invalid Type"},"description":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Description"},"contact_info":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Contact Info"}},"type":"object","required":["resource_id","pan_type","invalid_type"],"title":"ResourceInvalidFeedbackRequest"},"ResourceInvalidFeedbackResponse":{"properties":{"status":{"type":"string","title":"Status"},"message":{"type":"string","title":"Message"},"is_deleted":{"type":"boolean","title":"Is Deleted","default":false}},"type":"object","required":["status","message"],"title":"ResourceInvalidFeedbackResponse"},"ResourceSubmissionRequest":{"properties":{"urls":{"items":{"$ref":"#/components/schemas/ResourceUrlItem"},"type":"array","maxItems":2000,"minItems":1,"title":"Urls"},"is_mine":{"anyOf":[{"type":"boolean"},{"type":"null"}],"title":"Is Mine","description":"æ¯å¦ä¸ºç®¡çåæäº¤ (Trueè¡¨ç¤ºæ¯)","default":false},"is_parsed":{"anyOf":[{"type":"boolean"},{"type":"null"}],"title":"Is Parsed","description":"æ¯å¦æ è®°ä¸ºå·²è§£æï¼æ§å¶è¿ååå§é¾æ¥è¿æ¯è½¬å­é¾æ¥ï¼","default":true},"admin_submit":{"anyOf":[{"type":"boolean"},{"type":"null"}],"title":"Admin Submit","description":"ç®¡çåæäº¤æ è¯","default":false}},"type":"object","required":["urls"],"title":"ResourceSubmissionRequest","description":"èµæºæ¹éæäº¤è¯·æ±æ¨¡å"},"ResourceType":{"type":"string","enum":["movie","tv_series","software","game","music","book","document","other"],"title":"ResourceType","description":"èµæºç±»åæä¸¾"},"ResourceUrlItem":{"properties":{"url":{"type":"string","maxLength":2048,"minLength":10,"title":"Url"},"title":{"anyOf":[{"type":"string","maxLength":255},{"type":"null"}],"title":"Title","description":"ç¨æ·èªå®ä¹æ é¢ï¼å¦ææä¾åä¼åä½¿ç¨æ­¤æ é¢èéè§£æå¾å°çæ é¢"}},"type":"object","required":["url"],"title":"ResourceUrlItem","description":"åä¸ªæäº¤çURL"},"RoleResponse":{"properties":{"id":{"type":"integer","title":"Id"},"name":{"type":"string","title":"Name"},"display_name":{"type":"string","title":"Display Name"},"description":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Description"},"permissions":{"items":{"type":"string"},"type":"array","title":"Permissions"}},"type":"object","required":["id","name","display_name","description","permissions"],"title":"RoleResponse","description":"è§è²ååºæ¨¡å"},"SearchResponse":{"properties":{"status":{"type":"string","title":"Status"},"message":{"type":"string","title":"Message"},"total":{"type":"integer","title":"Total"},"results":{"items":{"$ref":"#/components/schemas/SearchResult"},"type":"array","title":"Results"}},"type":"object","required":["status","message","total","results"],"title":"SearchResponse"},"SearchResult":{"properties":{"resource_id":{"type":"string","title":"Resource Id"},"file_name":{"type":"string","title":"File Name"},"file_size":{"type":"string","title":"File Size"},"file_type":{"type":"string","title":"File Type"},"pan_type":{"type":"integer","title":"Pan Type"},"thumbnail":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Thumbnail"},"created_at":{"anyOf":[{},{"type":"null"}],"title":"Created At"},"updated_at":{"anyOf":[{},{"type":"null"}],"title":"Updated At"},"update_time":{"anyOf":[{},{"type":"null"}],"title":"Update Time"},"text_content":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Text Content"},"relevance_score":{"anyOf":[{"type":"number"},{"type":"null"}],"title":"Relevance Score"}},"type":"object","required":["resource_id","file_name","file_size","file_type","pan_type"],"title":"SearchResult"},"ShareLinkResponse":{"properties":{"status":{"type":"string","title":"Status"},"message":{"type":"string","title":"Message"},"share_url":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Share Url"},"share_pwd":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Share Pwd"},"title":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Title"},"file_type":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"File Type"},"expiry_days":{"anyOf":[{"type":"integer"},{"type":"null"}],"title":"Expiry Days"},"is_deleted":{"type":"boolean","title":"Is Deleted","default":false}},"type":"object","required":["status","message"],"title":"ShareLinkResponse"},"SubmittedResourceInfo":{"properties":{"url":{"type":"string","title":"Url"},"status":{"type":"string","title":"Status"},"resource_id":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Resource Id"},"task_id":{"anyOf":[{"type":"string","format":"uuid"},{"type":"null"}],"title":"Task Id"},"message":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Message"},"notes":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Notes"}},"type":"object","required":["url","status"],"title":"SubmittedResourceInfo","description":"åä¸ªURLæäº¤åçåæ­¥å¤çä¿¡æ¯"},"TaskStatus":{"type":"string","enum":["accepted","processing","success","failed_fetch_details","failed_parse_url","invalid_url","stuck_requeued","failed_duplicate"],"title":"TaskStatus","description":"åä¸ªèµæºæäº¤ä»»å¡ç¶æ"},"TimeFilter":{"type":"string","enum":["all","week","half_month","month","half_year","year"],"title":"TimeFilter","description":"æ¶é´è¿æ»¤éé¡¹æä¸¾"},"TokenRefreshRequest":{"properties":{"refresh_token":{"type":"string","title":"Refresh Token","description":"å·æ°ä»¤ç"}},"type":"object","required":["refresh_token"],"title":"TokenRefreshRequest","description":"ä»¤çå·æ°è¯·æ±æ¨¡å"},"TokenRefreshResponse":{"properties":{"access_token":{"type":"string","title":"Access Token","description":"æ°çè®¿é®ä»¤ç"},"token_type":{"type":"string","title":"Token Type","description":"ä»¤çç±»å","default":"bearer"},"expires_in":{"type":"integer","title":"Expires In","description":"è¿ææ¶é´ï¼ç§ï¼"}},"type":"object","required":["access_token","expires_in"],"title":"TokenRefreshResponse","description":"ä»¤çå·æ°ååºæ¨¡å"},"UserCreateRequest":{"properties":{"username":{"type":"string","maxLength":50,"minLength":3,"title":"Username","description":"ç¨æ·å"},"email":{"type":"string","format":"email","title":"Email","description":"é®ç®±å°å"},"password":{"type":"string","maxLength":128,"minLength":8,"title":"Password","description":"å¯ç "},"nickname":{"anyOf":[{"type":"string","maxLength":100},{"type":"null"}],"title":"Nickname","description":"æµç§°"},"role_id":{"type":"integer","title":"Role Id","description":"è§è²ID"},"status":{"type":"string","title":"Status","description":"ç¨æ·ç¶æ","default":"active"}},"type":"object","required":["username","email","password","role_id"],"title":"UserCreateRequest","description":"ç®¡çååå»ºç¨æ·è¯·æ±æ¨¡å"},"UserDetailResponse":{"properties":{"id":{"type":"integer","title":"Id"},"username":{"type":"string","title":"Username"},"email":{"type":"string","title":"Email"},"nickname":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Nickname"},"avatar":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Avatar"},"status":{"type":"string","title":"Status"},"email_verified":{"type":"boolean","title":"Email Verified"},"role":{"$ref":"#/components/schemas/RoleResponse"},"login_attempts":{"type":"integer","title":"Login Attempts"},"locked_until":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"title":"Locked Until"},"last_login_at":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"title":"Last Login At"},"created_at":{"type":"string","format":"date-time","title":"Created At"},"updated_at":{"type":"string","format":"date-time","title":"Updated At"}},"type":"object","required":["id","username","email","nickname","avatar","status","email_verified","role","login_attempts","locked_until","last_login_at","created_at","updated_at"],"title":"UserDetailResponse","description":"ç¨æ·è¯¦æååºæ¨¡å"},"UserLoginRequest":{"properties":{"username":{"type":"string","title":"Username","description":"ç¨æ·åæé®ç®±"},"password":{"type":"string","title":"Password","description":"å¯ç "},"remember_me":{"type":"boolean","title":"Remember Me","description":"è®°ä½æ","default":false}},"type":"object","required":["username","password"],"title":"UserLoginRequest","description":"ç¨æ·ç»å½è¯·æ±æ¨¡å"},"UserLoginResponse":{"properties":{"access_token":{"type":"string","title":"Access Token","description":"è®¿é®ä»¤ç"},"refresh_token":{"type":"string","title":"Refresh Token","description":"å·æ°ä»¤ç"},"token_type":{"type":"string","title":"Token Type","description":"ä»¤çç±»å","default":"bearer"},"expires_in":{"type":"integer","title":"Expires In","description":"è¿ææ¶é´ï¼ç§ï¼"},"user":{"allOf":[{"$ref":"#/components/schemas/UserProfileResponse"}],"description":"ç¨æ·ä¿¡æ¯"}},"type":"object","required":["access_token","refresh_token","expires_in","user"],"title":"UserLoginResponse","description":"ç¨æ·ç»å½ååºæ¨¡å"},"UserPasswordResetRequest":{"properties":{"new_password":{"type":"string","maxLength":128,"minLength":8,"title":"New Password","description":"æ°å¯ç "}},"type":"object","required":["new_password"],"title":"UserPasswordResetRequest","description":"ç®¡çåéç½®ç¨æ·å¯ç è¯·æ±æ¨¡å"},"UserProfileResponse":{"properties":{"id":{"type":"integer","title":"Id"},"username":{"type":"string","title":"Username"},"email":{"type":"string","title":"Email"},"nickname":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Nickname"},"avatar":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Avatar"},"status":{"type":"string","title":"Status"},"email_verified":{"type":"boolean","title":"Email Verified"},"role":{"$ref":"#/components/schemas/RoleResponse"},"points":{"type":"integer","title":"Points","description":"ç¨æ·ç§¯å","default":0},"title":{"type":"string","title":"Title","description":"ç¨æ·å¤´è¡","default":"èµæºæ¾èè"},"last_login_at":{"anyOf":[{"type":"string","format":"date-time"},{"type":"null"}],"title":"Last Login At"},"created_at":{"type":"string","format":"date-time","title":"Created At"}},"type":"object","required":["id","username","email","nickname","avatar","status","email_verified","role","last_login_at","created_at"],"title":"UserProfileResponse","description":"ç¨æ·èµæååºæ¨¡å"},"UserRegisterRequest":{"properties":{"username":{"type":"string","maxLength":50,"minLength":3,"title":"Username","description":"ç¨æ·å"},"email":{"type":"string","format":"email","title":"Email","description":"é®ç®±å°å"},"password":{"type":"string","maxLength":128,"minLength":8,"title":"Password","description":"å¯ç "},"nickname":{"anyOf":[{"type":"string","maxLength":100},{"type":"null"}],"title":"Nickname","description":"æµç§°"}},"type":"object","required":["username","email","password"],"title":"UserRegisterRequest","description":"ç¨æ·æ³¨åè¯·æ±æ¨¡å"},"UserUpdateRequest":{"properties":{"username":{"anyOf":[{"type":"string","maxLength":50,"minLength":3},{"type":"null"}],"title":"Username","description":"ç¨æ·å"},"email":{"anyOf":[{"type":"string","format":"email"},{"type":"null"}],"title":"Email","description":"é®ç®±å°å"},"nickname":{"anyOf":[{"type":"string","maxLength":100},{"type":"null"}],"title":"Nickname","description":"æµç§°"},"role_id":{"anyOf":[{"type":"integer"},{"type":"null"}],"title":"Role Id","description":"è§è²ID"},"status":{"anyOf":[{"type":"string"},{"type":"null"}],"title":"Status","description":"ç¨æ·ç¶æ"}},"type":"object","title":"UserUpdateRequest","description":"ç®¡çåæ´æ°ç¨æ·è¯·æ±æ¨¡å"},"ValidationError":{"properties":{"loc":{"items":{"anyOf":[{"type":"string"},{"type":"integer"}]},"type":"array","title":"Location"},"msg":{"type":"string","title":"Message"},"type":{"type":"string","title":"Error Type"}},"type":"object","required":["loc","msg","type"],"title":"ValidationError"},"ValidationResult":{"properties":{"valid":{"type":"boolean","title":"Valid","description":"æ¯å¦ææ"},"message":{"type":"string","title":"Message","description":"éªè¯æ¶æ¯"},"suggestions":{"anyOf":[{"items":{"type":"string"},"type":"array"},{"type":"null"}],"title":"Suggestions","description":"å»ºè®®"}},"type":"object","required":["valid","message"],"title":"ValidationResult","description":"éªè¯ç»æ"},"VerifyEmailChangeRequest":{"properties":{"token":{"type":"string","title":"Token","description":"éªè¯ä»¤ç"}},"type":"object","required":["token"],"title":"VerifyEmailChangeRequest","description":"éªè¯é®ç®±æ´æ¹è¯·æ±æ¨¡å"},"app__models__auth_models__ChangePasswordRequest":{"properties":{"current_password":{"type":"string","title":"Current Password","description":"å½åå¯ç "},"new_password":{"type":"string","maxLength":128,"minLength":8,"title":"New Password","description":"æ°å¯ç "}},"type":"object","required":["current_password","new_password"],"title":"ChangePasswordRequest","description":"ä¿®æ¹å¯ç è¯·æ±æ¨¡å"},"app__models__auth_models__UserProfileUpdateRequest":{"properties":{"nickname":{"anyOf":[{"type":"string","maxLength":100},{"type":"null"}],"title":"Nickname","description":"æµç§°"},"avatar":{"anyOf":[{"type":"string","maxLength":512},{"type":"null"}],"title":"Avatar","description":"å¤´åURL"}},"type":"object","title":"UserProfileUpdateRequest","description":"ç¨æ·èµææ´æ°è¯·æ±æ¨¡å"},"app__models__profile_models__ChangePasswordRequest":{"properties":{"old_password":{"type":"string","title":"Old Password","description":"æ§å¯ç "},"new_password":{"type":"string","maxLength":128,"minLength":8,"title":"New Password","description":"æ°å¯ç "}},"type":"object","required":["old_password","new_password"],"title":"ChangePasswordRequest","description":"ä¿®æ¹å¯ç è¯·æ±æ¨¡å"},"app__models__profile_models__UserProfileUpdateRequest":{"properties":{"nickname":{"anyOf":[{"type":"string","maxLength":100},{"type":"null"}],"title":"Nickname","description":"æµç§°"}},"type":"object","title":"UserProfileUpdateRequest","description":"ç¨æ·èµææ¹éæ´æ°è¯·æ±æ¨¡å"}},"securitySchemes":{"HTTPBearer":{"type":"http","scheme":"bearer"}}}}
