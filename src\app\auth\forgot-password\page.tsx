"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { forgotPassword } from "@/services/authService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import { Mail, ArrowLeft, AlertCircle, CheckCircle, Send } from "lucide-react";

export default function ForgotPasswordPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [emailSent, setEmailSent] = useState(false);

  // 如果已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      router.replace("/");
    }
  }, [isAuthenticated, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    
    // 清除错误信息
    if (error) setError(null);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError("请输入邮箱地址");
      return;
    }

    if (!validateEmail(email)) {
      setError("请输入有效的邮箱地址");
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await forgotPassword({ email });
      
      if (result.success) {
        setSuccess(result.message || "重置密码邮件已发送");
        setEmailSent(true);
      } else {
        setError(result.message || "发送失败，请稍后重试");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleResendEmail = async () => {
    if (loading) return;
    
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await forgotPassword({ email });
      
      if (result.success) {
        setSuccess("重置密码邮件已重新发送");
      } else {
        setError(result.message || "发送失败，请稍后重试");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 如果已登录，显示加载状态
  if (isAuthenticated) {
    return (
      <PageContainer>
        <div className="min-h-[60vh] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">正在跳转...</p>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="min-h-[80vh] flex items-center justify-center py-12">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-display">忘记密码</CardTitle>
              <CardDescription>
                {emailSent 
                  ? "我们已向您的邮箱发送了重置密码的链接"
                  : "输入您的邮箱地址，我们将发送重置密码的链接"
                }
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {/* 错误提示 */}
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* 成功提示 */}
              {success && (
                <Alert variant="success" className="mb-4">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              {!emailSent ? (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">邮箱地址</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="请输入您的邮箱地址"
                      value={email}
                      onChange={handleInputChange}
                      disabled={loading}
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        发送中...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        发送重置链接
                      </>
                    )}
                  </Button>
                </form>
              ) : (
                <div className="space-y-4">
                  <div className="text-center p-6 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800">
                    <Mail className="h-12 w-12 text-green-600 dark:text-green-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
                      邮件已发送
                    </h3>
                    <p className="text-sm text-green-700 dark:text-green-300 mb-4">
                      我们已向 <strong>{email}</strong> 发送了重置密码的链接。
                      请检查您的邮箱（包括垃圾邮件文件夹）。
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400">
                      链接将在24小时后过期
                    </p>
                  </div>

                  <div className="text-center space-y-2">
                    <p className="text-sm text-muted-foreground">
                      没有收到邮件？
                    </p>
                    <Button
                      variant="outline"
                      onClick={handleResendEmail}
                      disabled={loading}
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                          重新发送中...
                        </>
                      ) : (
                        <>
                          <Mail className="h-4 w-4 mr-2" />
                          重新发送邮件
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}

              <div className="mt-6 text-center space-y-2">
                <Link
                  href="/auth/login"
                  className="inline-flex items-center text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  返回登录
                </Link>
                
                <div className="text-sm text-muted-foreground">
                  还没有账户？{" "}
                  <Link
                    href="/auth/register"
                    className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors"
                  >
                    立即注册
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageContainer>
  );
}
