"use client";

import React, { useState } from "react";
import { verifyEmail, resendVerificationEmail } from "@/services/authService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { PageContainer } from "@/components/layout/PageContainer";
import { CheckCircle, AlertCircle, Mail, Send } from "lucide-react";

export default function TestEmailVerificationPage() {
  const [testToken, setTestToken] = useState("");
  const [testEmail, setTestEmail] = useState("");
  const [verifyResult, setVerifyResult] = useState<any>(null);
  const [resendResult, setResendResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleTestVerify = async () => {
    if (!testToken.trim()) {
      alert("请输入测试token");
      return;
    }

    setLoading(true);
    setVerifyResult(null);

    try {
      const result = await verifyEmail({ token: testToken });
      setVerifyResult(result);
    } catch (error) {
      setVerifyResult({
        success: false,
        message: "测试失败: " + (error as Error).message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestResend = async () => {
    if (!testEmail.trim()) {
      alert("请输入测试邮箱");
      return;
    }

    setLoading(true);
    setResendResult(null);

    try {
      const result = await resendVerificationEmail({ email: testEmail });
      setResendResult(result);
    } catch (error) {
      setResendResult({
        success: false,
        message: "测试失败: " + (error as Error).message,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer>
      <div className="py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-display font-bold">邮箱验证功能测试</h1>
          <p className="text-muted-foreground mt-2">
            测试邮箱验证和重新发送验证邮件功能
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 验证Token测试 */}
          <Card>
            <CardHeader>
              <CardTitle>验证Token测试</CardTitle>
              <CardDescription>
                测试邮箱验证API接口
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="test-token">测试Token</Label>
                <Input
                  id="test-token"
                  placeholder="输入验证token"
                  value={testToken}
                  onChange={(e) => setTestToken(e.target.value)}
                />
              </div>

              <Button
                onClick={handleTestVerify}
                disabled={loading}
                className="w-full"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                测试验证
              </Button>

              {verifyResult && (
                <Alert variant={verifyResult.success ? "success" : "destructive"}>
                  {verifyResult.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    <div>
                      <p><strong>结果:</strong> {verifyResult.success ? "成功" : "失败"}</p>
                      <p><strong>消息:</strong> {verifyResult.message}</p>
                      {verifyResult.error && (
                        <p><strong>错误:</strong> {verifyResult.error}</p>
                      )}
                      {verifyResult.data && (
                        <p><strong>数据:</strong> {JSON.stringify(verifyResult.data)}</p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* 重新发送邮件测试 */}
          <Card>
            <CardHeader>
              <CardTitle>重新发送邮件测试</CardTitle>
              <CardDescription>
                测试重新发送验证邮件API接口
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="test-email">测试邮箱</Label>
                <Input
                  id="test-email"
                  type="email"
                  placeholder="输入邮箱地址"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                />
              </div>

              <Button
                onClick={handleTestResend}
                disabled={loading}
                className="w-full"
              >
                <Send className="h-4 w-4 mr-2" />
                测试重新发送
              </Button>

              {resendResult && (
                <Alert variant={resendResult.success ? "success" : "destructive"}>
                  {resendResult.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertCircle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    <div>
                      <p><strong>结果:</strong> {resendResult.success ? "成功" : "失败"}</p>
                      <p><strong>消息:</strong> {resendResult.message}</p>
                      {resendResult.error && (
                        <p><strong>错误:</strong> {resendResult.error}</p>
                      )}
                      {resendResult.data && (
                        <p><strong>数据:</strong> {JSON.stringify(resendResult.data)}</p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 测试链接 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>测试链接</CardTitle>
            <CardDescription>
              快速测试不同场景的验证链接
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm">
                <strong>有效token测试:</strong>{" "}
                <a
                  href="/auth/verify-email?token=valid-test-token"
                  className="text-blue-600 hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  /auth/verify-email?token=valid-test-token
                </a>
              </p>
              <p className="text-sm">
                <strong>无效token测试:</strong>{" "}
                <a
                  href="/auth/verify-email?token=invalid-token"
                  className="text-blue-600 hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  /auth/verify-email?token=invalid-token
                </a>
              </p>
              <p className="text-sm">
                <strong>无token测试:</strong>{" "}
                <a
                  href="/auth/verify-email"
                  className="text-blue-600 hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  /auth/verify-email
                </a>
              </p>
              <p className="text-sm">
                <strong>实际token测试:</strong>{" "}
                <a
                  href="/auth/verify-email?token=a88xcP-8Sp3pm59TGuNltBljmD19MoTLSfs6Zeo8kyQ"
                  className="text-blue-600 hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  /auth/verify-email?token=a88xcP-8Sp3pm59TGuNltBljmD19MoTLSfs6Zeo8kyQ
                </a>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* API信息 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>API信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>验证邮箱API:</strong> POST /api/auth/verify-email</p>
              <p><strong>重新发送API:</strong> POST /api/auth/resend-verification</p>
              <p><strong>当前环境:</strong> {process.env.NODE_ENV}</p>
              <p><strong>API Base URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
