/**
 * 测试循环依赖是否已修复
 */

console.log("开始测试循环依赖修复...");

try {
  // 测试导入authService
  console.log("1. 测试导入authService...");
  const authService = require("./services/authService");
  console.log("✅ authService导入成功");

  // 测试导入tokenManager
  console.log("2. 测试导入tokenManager...");
  const tokenManager = require("./utils/tokenManager");
  console.log("✅ tokenManager导入成功");

  // 测试导入profileService
  console.log("3. 测试导入profileService...");
  const profileService = require("./services/profileService");
  console.log("✅ profileService导入成功");

  console.log("🎉 所有模块导入成功，循环依赖问题已修复！");
} catch (error) {
  console.error("❌ 导入失败:", error.message);
  console.error("详细错误:", error);
}
